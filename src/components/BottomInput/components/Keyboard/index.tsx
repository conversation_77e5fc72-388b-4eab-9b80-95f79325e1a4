import useObjState from '@/hooks/useObjState'
import { Input, Image } from '@tarojs/components'
import Slice77Img from '@/assets/images/input/slice-77.png'
import Slice78Img from '@/assets/images/input/slice-78.png'
import Slice79Img from '@/assets/images/input/slice-79.png'
import Slice80Img from '@/assets/images/input/slice-80.png'
import closeImg from '@/assets/images/chat/close.png'
import stopImg from '@/assets/images/chat/stop.png'
import Taro from '@tarojs/taro'
import txImg from '@/assets/images/chat/tx.png'
import { ActionSheet } from '@taroify/core'
import { useMemo, useState } from 'react'

interface PendingImage {
  url: string
  localPath: string
}

export default function Keyboard({
  sendMessage,
  chooseImage,
  isTool = true,
  inputType,
  loading = false,
  cancelCurrentRun,
  pendingImages = [],
  removeImage
}: {
  sendMessage: (message: string) => Promise<void> | void
  chooseImage: (type: string[]) => void
  isTool?: boolean
  inputType: any
  loading?: boolean
  cancelCurrentRun?: () => Promise<void>
  pendingImages?: PendingImage[]
  removeImage?: (index: number) => void
}) {
  const keyword = useObjState('')
  const [open, setOpen] = useState(false)

  const actions = useMemo(
    () => [
      { name: '拍照', value: 'camera' },
      { name: '相册', value: 'album' }
    ],
    []
  )

  return (
    <div className="px-[20px] relative">
      {/* 浮动工具栏区域 */}
      <div className="absolute top-[-80px] left-[20px] flex items-center gap-[12px]">
        {isTool && (
          <div
            className="w-[186px] h-[72px] rounded-[16px] flex_center bg-[#F8F8F8]"
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/canvas/index`
              })
            }}
          >
            <img className="w-[42px] h-[40px] mr-[10px]" src={txImg} alt="" />
            <div className="flex items-center font-normal text-[24px] text-black leading-[24px] text-left not-italic">定制T恤</div>
          </div>
        )}

        {/* 图片预览区域 - 与定制T恤同一行 */}
        {pendingImages.length > 0 && (
          <div className="h-[72px] px-[16px] bg-[#F8F8F8] rounded-[16px] flex items-center gap-[8px]">
            {pendingImages.map((image, index) => (
              <div key={index} className="relative w-[56px] h-[56px] rounded-[8px]">
                <img className="w-full h-full object-cover" src={image.localPath || image.url} alt="" />
                {/* 删除按钮 */}
                <div
                  className="absolute top-[-15px] right-[-25px] w-[40px] h-[40px] rounded-full flex_center"
                  onClick={() => removeImage?.(index)}
                >
                  <img src={closeImg} className="flex_center w-full h-full" />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      <div className="h-[112px] rounded-[24px] bg-white shadow-[rgba(0,0,0,0.05)_0px_6px_24px_0px,rgba(0,0,0,0.08)_0px_0px_0px_1px] flex items-center justify-between relative">
        {/* <div onClick={() => chooseImage(['camera'])} className="ml-[24px] mr-[8px] flex_center">
          <Image className="w-[48px] h-[48px]" src={Slice77Img} />
        </div> */}
        <div className="flex-1 h-full ml-[20px]">
          <Input
            className="w-full h-[112px] flex items-center outline-none border-none font-normal text-[24px] text-[#2A3447] leading-[32px] not-italic normal-case"
            type="text"
            confirm-type="send"
            placeholder="输入需求生成 T 恤设计"
            name="message"
            value={keyword.val}
            adjustPosition={false}
            onInput={(e) => {
              keyword.set(e.detail.value)
            }}
            // 回车提交表单
            onConfirm={() => {
              if (keyword.val.trim() || pendingImages.length > 0) {
                sendMessage(keyword.val)
                keyword.set('') // 清空输入框
              }
            }}
          />
        </div>
        {/* <div onClick={() => inputType.set((v: string) => (v === 'text' ? 'record' : 'text'))} className="ml-[8px] flex_center">
          <Image className="w-[48px] h-[48px]" src={inputType.val === 'text' ? Slice79Img : Slice80Img} />
        </div> */}
        {/* <div onClick={() => chooseImage(['album'])} className="mr-[24px] ml-[16px] flex_center"> */}
        <div
          onClick={() => {
            if (loading && cancelCurrentRun) {
              // 如果正在加载且有取消函数，则执行取消操作
              cancelCurrentRun()
            } else {
              // 否则打开选择图片的弹窗
              setOpen(true)
            }
          }}
          className="mr-[24px] ml-[16px] flex_center"
        >
          <Image className="w-[48px] h-[48px]" src={loading ? stopImg : Slice78Img} />
        </div>
      </div>

      <ActionSheet
        cancelText="取消"
        actions={actions}
        open={open && !loading} // 在loading状态下不显示ActionSheet
        onSelect={(e) => {
          // console.log('e', e)
          chooseImage([e.value])
          setOpen(false)
        }}
        onCancel={() => setOpen(false)}
        onClose={setOpen}
      />
    </div>
  )
}
