import Taro from '@tarojs/taro'
import { atom } from 'jotai'

// 获取地址栏上面的ThreadId
let addrThreadId: string | null = null
// 获取H5地址栏上面的ThreadId
if (Taro.getEnv() === 'WEB') {
  const urlParams = new URLSearchParams(window.location.search)
  addrThreadId = urlParams.get('threadId') || null
} else {
  addrThreadId = Taro.getCurrentInstance().router?.params?.threadId || null
}
// 获取本地存储的ThreadId
const storageThreadId: string | null = Taro.getStorageSync('threadId') || null
if (!storageThreadId && addrThreadId) {
  Taro.setStorageSync('threadId', addrThreadId)
}
export const currentThreadIdState = atom<string | null>(addrThreadId || storageThreadId || null)

export interface MessageMetadata {
  finish_reason: 'stop'
  model_name: string
  service_tier: string
}

export interface Message {
  tool_calls?: any[]
  content: string
  additional_kwargs?: any
  response_metadata: MessageMetadata
  type: 'human' | 'ai' | 'AIMessageChunk' | 'tool'
  name: string | null
  id: string
  example: boolean
  preview?: string
}

export const messgaeListState = atom<Message[]>([])
