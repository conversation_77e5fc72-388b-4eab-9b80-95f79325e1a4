import BottomInput from '@/components/BottomInput'
import { currentThreadIdState, Message, messgaeListState } from '@/store/chat'
import useObjAtom from '@/hooks/useObjAtom'
import { Loading } from '@taroify/core'
import Taro, { requirePlugin } from '@tarojs/taro'
import { useCallback, useEffect, useRef } from 'react'
import useObjState from '@/hooks/useObjState'
import { userinfoState } from '@/store/global'
import { useAtomValue } from 'jotai'
import { decodeUint8Array } from '@/utils'
import { MessageText } from './components/MessageText'
import { MessageChatImage } from './components/MessageChatImage'
import { MessageCanvas } from './components/MessageCanvas'
import NavBarTitle from './components/NavBarTitle'

let plugin = requirePlugin('QCloudAIVoice')
plugin.setQCloudSecret(1309533950, 'AKIDe9RVN2VlpfIhtrc9UFo9GH4e2GQWTGrI', 'UmwwmPzQfllbJqBJH2iCWecjkaJ5kFXg', true) //设置腾讯云账号信息，其中appid（非微信appid）是数字，secret是字符串，

const Index = () => {
  const isWx = Taro.getEnv() === 'WEAPP'
  const chatListRef = useRef<HTMLDivElement>(null)
  const scrollToBottomRef = useRef<boolean>(false)
  const isScrollToBottom = useObjState(true) // 记录是否滚动到底部，用于判断是否需要滚动到底部
  const currentThreadId = useObjAtom(currentThreadIdState)
  const messgaeList = useObjAtom(messgaeListState)
  const keyboardHeight = useObjState(0) // 键盘高度
  const userinfo = useAtomValue(userinfoState)
  const title = useObjState('')
  const loading = useObjState(false)

  // 用于记录是否是最后一个分块
  const isLastChunk = useRef(false)
  // 用于缓存未完成的 JSON 数据
  const incompleteDataRef = useRef<string>('')
  // 用于存储当前运行的 run_id
  const currentRunId = useRef<string | null>(null)
  // 用于记录最后一次收到数据的时间
  const lastDataTimeRef = useRef<number>(0)
  // 用于存储流状态检查定时器
  const streamStateCheckRef = useRef<NodeJS.Timeout | null>(null)

  const init = useCallback(async () => {
    // 获取threadId
    let threadId = currentThreadId.val
    if (!threadId) {
      threadId = await new Promise((resolve) => {
        Taro.request({
          url: `${process.env.TARO_APP_API_AI}/agent/threads`,
          method: 'POST',
          data: { metadata: { userId: userinfo?.userId || 1 } }
        }).then((res) => {
          resolve(res.data.thread_id)
        })
      })
      currentThreadId.set(threadId)
      Taro.setStorageSync('threadId', threadId)
    }

    // 获取历史消息
    Taro.request({
      url: `${process.env.TARO_APP_API_AI}/agent/threads/${threadId}`,
      method: 'GET'
    }).then((res) => {
      messgaeList.set(res.data.values?.messages || [])

      // 提取title
      const content = res.data.values?.messages[0].content
      // 判断content是否为图片，如果是图片，赋值为图片文件
      // 处理 'img:xxx,xxx,xxx;text:xxx' 格式
      if (content && content.includes('img:')) {
        try {
          // 分离图片和文本部分
          const parts = content.split(';')

          // 解析文本部分
          const textPart = parts.find((part) => part.startsWith('text:'))
          if (textPart) {
            title.set(textPart.replace('text:', ''))
          } else {
            title.set('[图片]') // 如果没有文本部分，显示默认文本
          }
        } catch (error) {
          title.set('[图片]')
        }
      } else {
        // 如果没有图片，但有text:前缀，则去除前缀
        if (content && content.startsWith('text:')) {
          title.set(content.replace('text:', ''))
        } else {
          title.set(content || '')
        }
      }

      // 延迟滚动到底部
      setTimeout(() => {
        scrollToBottom?.()
      }, 100)

      // 发送暂存消息
      Taro.getStorage({ key: 'chatMessageData' })
        .then((storage) => {
          console.log(storage.data)
          if (storage.data) {
            // 延迟发送消息，确保页面已经渲染完成
            setTimeout(() => {
              sendMessage({ text: storage.data.text })
            }, 200)
            Taro.removeStorage({ key: 'chatMessageData' })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    })
  }, [currentThreadId.val, messgaeList, userinfo])

  useEffect(() => {
    init()
    return () => {
      stopStreamStateMonitoring()
    }
  }, [])

  const playTTS = () => {
    const text =
      '腾讯云基于业界领先技术构建的语音合成系统，具备合成速度快、合成拟真度高、语音自然流畅等特点，能够应用于多种使用场景，让设备和应用轻松发声。'
    const innerAudioContext = Taro.createInnerAudioContext()
    plugin.textToSpeech({
      content: text,
      speed: 0,
      volume: 0,
      voiceType: 501004,
      language: 1,
      projectId: 0,
      sampleRate: 16000,

      success: function (data) {
        let url = data.result.filePath // data.result.filePath返回的url有效期为1分钟，若需要播放，建议自行存储音频数据
        if (url && url.length > 0) {
          innerAudioContext.autoplay = true
          innerAudioContext.src = url
          innerAudioContext.onPlay(() => {})
          innerAudioContext.onError((res) => {
            console.log(res.errMsg)
          })
        }
      },
      fail: function (error) {
        console.log(error)
      }
    })
  }

  // 监听键盘高度变化
  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const onKeyboardHeightChange = (res: { height: number }) => {
        console.log('chat页面 键盘高度变化:', res.height)
        keyboardHeight.set(res.height)
      }

      Taro.onKeyboardHeightChange(onKeyboardHeightChange)

      return () => {}
    }
  }, [])

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (!scrollToBottomRef.current) {
      scrollToBottomRef.current = true
      setTimeout(() => {
        if (isWx) {
          // 微信小程序环境，使用 pageScrollTo
          Taro.createSelectorQuery()
            .select('.chat-container')
            .boundingClientRect((rect) => {
              if (rect && !Array.isArray(rect)) {
                Taro.pageScrollTo({
                  scrollTop: rect.height,
                  duration: 300
                })
              }
            })
            .exec()
        } else {
          // H5 环境，直接操作 DOM
          chatListRef.current?.scrollTo({
            top: chatListRef.current.scrollHeight,
            behavior: 'smooth'
          })
        }
        isScrollToBottom.set(true)
        scrollToBottomRef.current = false
      }, 250)
    }
  }, [isWx, isScrollToBottom])

  // 停止流状态监控
  const stopStreamStateMonitoring = useCallback(() => {
    if (streamStateCheckRef.current) {
      clearTimeout(streamStateCheckRef.current)
      streamStateCheckRef.current = null
    }
  }, [])

  // 取消当前运行的函数
  const cancelCurrentRun = useCallback(async () => {
    // 停止流状态监控
    stopStreamStateMonitoring()

    if (currentRunId.current) {
      const threadId = currentThreadId.get()
      try {
        console.log('取消当前运行:', currentRunId.current)
        await Taro.request({
          url: `${process.env.TARO_APP_API_AI}/agent/threads/${threadId}/runs/${currentRunId.current}/cancel`,
          method: 'POST'
        })
        console.log('成功取消运行:', currentRunId.current)
      } catch (error) {
        console.error('取消运行失败:', error)
      }
    }
    currentRunId.current = null
    loading.set(false)
  }, [currentThreadId, stopStreamStateMonitoring])

  // 解析分块数据的函数
  const parseChunkData = useCallback((chunkText: string) => {
    if (incompleteDataRef.current) {
      console.log('拼接缓存数据')
      chunkText = incompleteDataRef.current + chunkText
      incompleteDataRef.current = ''
    }

    const entries: any[] = []

    // 按行分割数据
    const lines = chunkText.split('\n').filter((line) => line.trim())
    console.log('lines', lines)

    let currentEntry: any = {}

    for (const line of lines) {
      if (line.startsWith('event:')) {
        // 如果已有完整的entry，先保存
        if (currentEntry.event && currentEntry.data) {
          entries.push(currentEntry)
          currentEntry = {}
        }
        currentEntry.event = line.replace('event:', '').trim()
      } else if (line.startsWith('data:')) {
        try {
          const dataStr = line.replace('data:', '').trim()
          currentEntry.data = JSON.parse(dataStr)
        } catch (e) {
          // console.warn('解析data失败:', line)
          // 如果解析失败，可能是数据不完整，缓存起来
          incompleteDataRef.current = chunkText
          return []
        }
      } else if (line.startsWith('id:')) {
        currentEntry.id = line.replace('id:', '').trim()
      }
    }

    // 保存最后一个entry
    if (currentEntry.event && currentEntry.data) {
      entries.push(currentEntry)
    }
    console.log('entries', entries)
    return entries
  }, [])

  // 处理消息条目的函数
  const processMessageEntry = useCallback(
    (entry: any) => {
      if (!entry.data) return

      let messageData: Message

      // 根据数据格式提取消息内容
      if (Array.isArray(entry.data)) {
        // 如果是数组，取下标0的数据
        messageData = entry.data[0]
      } else if (entry.data.messages && Array.isArray(entry.data.messages)) {
        // 如果是对象且有messages数组，取messages[0]的数据
        messageData = entry.data.messages[entry.data.messages.length - 1]
      } else {
        return
      }

      if (!messageData || !messageData.id || messageData.content === undefined) return

      // console.log('messageData', messageData)
      const { id, content, type, response_metadata, name, tool_calls } = messageData

      // 更新messageList
      const currentMessages = messgaeList.get()
      const existingMessageIndex = currentMessages.findIndex((msg) => msg.id === id)

      if (existingMessageIndex >= 0) {
        // 更新已存在的消息
        const updatedMessages = [...currentMessages]
        updatedMessages[existingMessageIndex] = {
          ...updatedMessages[existingMessageIndex],
          name,
          content: updatedMessages[existingMessageIndex].content + content,
          response_metadata: response_metadata || updatedMessages[existingMessageIndex].response_metadata,
          tool_calls: tool_calls || updatedMessages[existingMessageIndex].tool_calls
        }
        messgaeList.set(updatedMessages)
      } else {
        // 添加新消息
        const newMessage = {
          content: content,
          additional_kwargs: {},
          response_metadata: response_metadata || {},
          type: type === 'AIMessageChunk' ? 'ai' : type,
          name,
          id,
          example: false,
          tool_calls: tool_calls
        }
        messgaeList.set([...currentMessages, newMessage])
      }

      // 实时TTS播放（仅H5端，且AI消息）
      if ((type === 'ai' || type === 'AIMessageChunk') && content) {
        // 只处理纯文本
        const plainText = content.replace(/!\[.*?\]\(.*?\)/g, '') // 去除markdown图片
        // isWx && isPlay && synthesizeQwenTTSRealtime(plainText)
      }

      // 滚动到底部
      if (isScrollToBottom) {
        scrollToBottom?.()
      }
    },
    [isScrollToBottom, messgaeList, scrollToBottom]
  )

  const checkStreamState = useCallback(async () => {
    const threadId = currentThreadId.get()
    if (!currentRunId.current) {
      return true
    }

    try {
      const res = await Taro.request({
        url: `${process.env.TARO_APP_API_AI}/agent/threads/${threadId}/state`,
        method: 'GET'
      })
      console.log('checkStreamState', res)
      // 检查流是否空闲（根据实际API响应结构调整）
      if (res.data.next.length === 0 && res.data.tasks.length === 0) {
        return true
      }
      return false
    } catch (error) {
      console.error('检查流状态失败:', error)
      return true // 出错时认为流已结束
    }
  }, [currentThreadId])

  // 启动流状态监控
  const startStreamStateMonitoring = useCallback(() => {
    // 清除之前的定时器
    stopStreamStateMonitoring()

    const checkAndScheduleNext = async () => {
      // 检查是否在2秒内收到了新数据
      const timeSinceLastData = Date.now() - lastDataTimeRef.current

      if (timeSinceLastData >= 2000) {
        // 超过2秒未收到数据，检查流状态
        console.log('检查流状态，距离上次收到数据:', timeSinceLastData, 'ms')

        try {
          const isIdle = await checkStreamState()

          if (isIdle) {
            // 流空闲，停止loading
            console.log('流已空闲，停止loading')
            loading.set(false)
            currentRunId.current = null
            return // 不再继续检查
          } else {
            // 流不空闲，延时2秒后继续检测
            console.log('流未空闲，2秒后继续检测')
            streamStateCheckRef.current = setTimeout(checkAndScheduleNext, 2000)
          }
        } catch (error) {
          console.error('检查流状态出错:', error)
          // 出错时停止loading
          loading.set(false)
          currentRunId.current = null
        }
      } else {
        // 还未到2秒，继续等待
        const remainingTime = 2000 - timeSinceLastData
        streamStateCheckRef.current = setTimeout(checkAndScheduleNext, remainingTime)
      }
    }

    // 开始第一次检查（2秒后）
    streamStateCheckRef.current = setTimeout(checkAndScheduleNext, 2000)
  }, [checkStreamState, loading])

  // send
  const sendMessage = async ({ mode = 'messgae', text = '' }: { mode?: 'messgae' | 'continue'; text?: string }) => {
    // 如果当前有流正在运行，先取消它
    await cancelCurrentRun()

    const threadId = currentThreadId.get()
    console.log('sendMessage threadId', threadId)

    if (!title.val) {
      // if (type === 'image') {
      //   title.set('[图片]')
      // } else {
      //   title.set(text || '')
      // }
    }

    const data: any = {
      stream_mode: ['messages-tuple', 'values'],
      assistant_id: process.env.TARO_APP_ASSISTANT_ID,
      on_disconnect: 'cancel'
    }
    if (mode === 'continue') {
      data.command = { resume: { action: 'continue' } }
    } else {
      data.input = { messages: [{ type: 'human', content: text }] }
    }

    if (Taro.getEnv() === 'WEAPP') {
      // 微信小程序环境下的实现
      // @ts-ignore
      const requestTask = wx.request({
        url: `${process.env.TARO_APP_API_AI}/agent/threads/${threadId}/runs/stream`, //仅为示例，并非真实的接口地址
        method: 'POST',
        data,
        enableChunked: true
      })

      isLastChunk.current = false

      requestTask.onChunkReceived((res) => {
        let chunkText = decodeUint8Array(res.data)
        // console.log('chunkText', chunkText)

        // 解析分块数据，每个分块可能包含多条数据
        const dataEntries = parseChunkData(chunkText)

        console.log('dataEntries', dataEntries)

        // 更新最后收到数据的时间
        lastDataTimeRef.current = Date.now()

        // 监控流状态
        startStreamStateMonitoring()

        // 重置流超时定时器

        // 处理数据条目
        dataEntries.forEach((entry, index) => {
          // 处理第一个分块的第一条数据（metadata 事件，包含 run_id）
          if (index === 0 && entry.event === 'metadata') {
            loading.set(true)
            if (entry.data && entry.data.run_id) {
              currentRunId.current = entry.data.run_id
            }
            return
          }

          // 判断是否为最后一条数据
          const isLastEntry = index === dataEntries.length - 1
          // 检查是否为汇总数据，如果是则跳过处理
          if (isLastEntry && entry.event === 'values' && isLastChunk.current) {
            console.log('跳过汇总数据:', entry)
            const messageData = entry.data.messages[entry.data.messages.length - 1]
            if (messageData.tool_calls && messageData.tool_calls.length) {
              const toolCalls = messageData.tool_calls[0]
              if (toolCalls.name === 'gen_canvas_tool') {
                // 添加新消息
                const newMessage: Message = {
                  tool_calls: messageData.tool_calls,
                  content: '',
                  additional_kwargs: {},
                  response_metadata: messageData.response_metadata || {},
                  type: 'ai',
                  name: null,
                  id: messageData.id,
                  example: false
                }
                upload(newMessage)

                // 执行完成
                sendMessage({ mode: 'continue' })
              }
            }
            return
          }

          // 检查是否为最后一个分块
          if (!isLastChunk.current && entry.event === 'values') {
            isLastChunk.current = true
          }

          // 处理普通消息数据
          processMessageEntry(entry)
        })
      })
    } else {
      // H5 环境下使用 fetch 实现
      // const url = `${process.env.TARO_APP_API_AI}/agent/threads/${threadId}/runs/stream`
      // const body = JSON.stringify({
      //   input: { messages: [{ type: 'human', content: text }] },
      //   stream_mode: ['messages-tuple', 'values'],
      //   assistant_id: process.env.TARO_APP_ASSISTANT_ID,
      //   on_disconnect: 'cancel'
      // })
      // isFirstChunk.current = true
      // isLastChunk.current = false
      // fetch(url, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json'
      //   },
      //   body
      // })
      //   .then((response) => {
      //     const reader = response.body?.getReader()
      //     const decoder = new TextDecoder('utf-8')
      //     const readStream = async () => {
      //       if (!reader) return
      //       let done = false
      //       while (!done) {
      //         const { value, done: readerDone } = await reader.read()
      //         done = readerDone
      //         if (done) {
      //           // 流读取完成，重置状态
      //           console.log('H5 流读取完成，重置状态')
      //           resetStreamState()
      //           break
      //         }
      //         if (value) {
      //           console.log('value', value)
      //           const chunkText = decoder.decode(value, { stream: true })
      //           // console.log('chunkText', chunkText)
      //           // 解析分块数据，每个分块可能包含多条数据
      //           const dataEntries = parseChunkData(chunkText)
      //           // 处理数据条目
      //           dataEntries.forEach((entry, index) => {
      //             // 检查是否为流结束事件
      //             if (entry.event === 'end' || entry.event === 'done' || entry.event === 'close') {
      //               console.log('检测到流结束事件:', entry.event)
      //               resetStreamState()
      //               return
      //             }
      //             // 处理第一个分块的第一条数据（metadata 事件，包含 run_id）
      //             if (isFirstChunk.current && index === 0 && entry.event === 'metadata') {
      //               console.log('处理 metadata 事件:', entry)
      //               if (entry.data && entry.data.run_id) {
      //                 currentRunId.current = entry.data.run_id
      //                 isStreamRunning.current = true
      //                 loading.set(true) // 流开始时设置loading为true
      //                 console.log('存储 run_id:', entry.data.run_id)
      //                 // 启动全局状态监控
      //                 if (globalCheckRef.current) {
      //                   clearTimeout(globalCheckRef.current)
      //                 }
      //                 globalCheckRef.current = setTimeout(() => {
      //                   // 如果5分钟后流还在运行，强制重置（防止极端情况）
      //                   if (isStreamRunning.current && loading.val) {
      //                     console.log('流运行超过5分钟，强制重置状态')
      //                     resetStreamState()
      //                   }
      //                 }, 300000) // 5分钟
      //                 // 设置超时检测，60秒后如果流还没结束就强制结束
      //                 if (streamTimeoutRef.current) {
      //                   clearTimeout(streamTimeoutRef.current)
      //                 }
      //                 streamTimeoutRef.current = setTimeout(() => {
      //                   console.log('流执行超时，强制结束')
      //                   resetStreamState()
      //                 }, 60000) // 60秒超时
      //               }
      //               isFirstChunk.current = false
      //               return
      //             }
      //             // 判断是否为最后一条数据
      //             const isLastEntry = index === dataEntries.length - 1
      //             // 检查是否为汇总数据，如果是则跳过处理
      //             if (isLastEntry && entry.event === 'values' && isLastChunk.current) {
      //               console.log('跳过汇总数据:', entry)
      //               const messageData = entry.data.messages[entry.data.messages.length - 1]
      //               if (messageData.tool_calls && messageData.tool_calls.length) {
      //                 const toolCalls = messageData.tool_calls[0]
      //                 if (toolCalls.name === 'gen_canvas_tool') {
      //                   // 添加新消息
      //                   const newMessage: Message = {
      //                     tool_calls: messageData.tool_calls,
      //                     content: '',
      //                     additional_kwargs: {},
      //                     response_metadata: messageData.response_metadata || {},
      //                     type: 'ai',
      //                     name: null,
      //                     id: messageData.id,
      //                     example: false
      //                   }
      //                   upload(newMessage)
      //                 }
      //               }
      //               // 汇总数据通常是流的最后一个数据包，设置一个短暂的延迟来检测是否真的结束了
      //               console.log('汇总数据处理完成，设置延迟检测流结束')
      //               setTimeout(() => {
      //                 // 如果3秒后仍然没有新的数据且状态还在运行，则认为流已结束
      //                 if (isStreamRunning.current && loading.val) {
      //                   const timeSinceLastData = Date.now() - lastDataTimeRef.current
      //                   if (timeSinceLastData > 3000) {
      //                     // 3秒没有新数据
      //                     console.log('汇总数据后3秒无新数据，认为流已结束，重置状态')
      //                     resetStreamState()
      //                   }
      //                 }
      //               }, 3000)
      //               return
      //             }
      //             // 检查是否为最后一个分块
      //             if (!isLastChunk.current && entry.event === 'values') {
      //               isLastChunk.current = true
      //             }
      //             // 处理普通消息数据
      //             processMessageEntry(entry)
      //           })
      //         }
      //       }
      //     }
      //     readStream()
      //   })
      //   .catch((error) => {
      //     console.error('Fetch 请求错误:', error)
      //   })
    }
  }

  const upload = async (diyData: Message) => {
    console.log('diyData', diyData)
    if (!diyData.tool_calls) {
      return
    }

    const res = await Taro.request({
      url: `${process.env.TARO_APP_API_AI}/server/v1/genimage/preview`,
      method: 'POST',
      data: diyData.tool_calls[0].args.canvas[0]
    })

    messgaeList.set((v) => {
      const findMessageIndex = v.findIndex((item) => item.id === diyData.id)
      if (findMessageIndex >= 0) {
        const updatedMessages = [...v]
        updatedMessages[findMessageIndex] = {
          ...v[findMessageIndex],
          ...diyData,
          id: v[findMessageIndex].id,
          preview: res.data.output,
          content: v[findMessageIndex].content || diyData.content
        }
        return updatedMessages
      } else {
        return [...v, { ...diyData, preview: res.data.output }]
      }
    })
    scrollToBottom?.()
  }

  // 滚动事件
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10
      // console.log('first', scrollHeight, clientHeight, scrollTop)
      isScrollToBottom.set(isAtBottom)
    },
    [isScrollToBottom]
  )

  console.log('messgaeList.val', messgaeList.val)

  return (
    <>
      <div className="w-full h-full bg-white overflow-hidden flex flex-col">
        <NavBarTitle title={title.val} />
        {/* <div onClick={() => upload()}>AAA</div> */}
        <div
          ref={chatListRef}
          onScroll={handleScroll}
          className="chat-container relative flex-1 overflow-y-auto overflow-x-hidden pt-[30px] pb-[252px]"
        >
          {messgaeList.val.map((message, index) => {
            const chatgpt_text_image =
              message.name === 'chatgpt_text_image' || message.name === 'chatgpt_edit_image' || message.name === 'doubao_text_image'
            const isContinue = message.name === 'gen_canvas_tool' && message.type === 'tool'
            const gen_canvas_tool = message.tool_calls && message.tool_calls.length && message.tool_calls[0].name === 'gen_canvas_tool'
            if (isContinue) {
              return null
            }
            return (
              <div key={message.id}>
                {!chatgpt_text_image && !gen_canvas_tool ? <MessageText message={message} /> : null}
                {chatgpt_text_image ? <MessageChatImage message={{ ...message }} /> : null}
                {gen_canvas_tool ? <MessageCanvas message={message} threadId={currentThreadId.val} /> : null}
              </div>
            )
          })}
          {loading.val && (
            <div className="flex justify-start items-center px-[20px]">
              <Loading type="spinner" size="18px" />
              <div className="ml-[10px] text-[22px] text-[#999999]">思考中...</div>
            </div>
          )}
        </div>
        {/* {isWx && <div className="h-[154px]"></div>} */}
      </div>
      <div
        className="fixed z-50 left-0 w-full h-[112px] transition-all duration-300 ease-in-out"
        style={{
          bottom: keyboardHeight.val > 0 ? `${keyboardHeight.val + 20}px` : '34px'
        }}
      >
        <BottomInput sendMessage={(text) => sendMessage({ text })} loading={loading.val} cancelCurrentRun={cancelCurrentRun} />
      </div>
    </>
  )
}

export default Index
