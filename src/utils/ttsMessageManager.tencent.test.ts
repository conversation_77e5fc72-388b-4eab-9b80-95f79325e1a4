// TTS消息管理器腾讯云TTS测试文件
// 用于测试腾讯云TTS集成功能

import { ttsMessageManager } from './ttsMessageManager'

// 模拟腾讯云TTS插件
const mockPlugin = {
  setQCloudSecret: jest.fn(),
  textToSpeech: jest.fn()
}

// 模拟Taro
const mockTaro = {
  createInnerAudioContext: jest.fn(() => ({
    autoplay: false,
    src: '',
    onPlay: jest.fn(),
    onEnded: jest.fn(),
    onError: jest.fn(),
    stop: jest.fn(),
    destroy: jest.fn()
  }))
}

// 模拟requirePlugin
jest.mock('@tarojs/taro', () => ({
  ...jest.requireActual('@tarojs/taro'),
  createInnerAudioContext: mockTaro.createInnerAudioContext,
  requirePlugin: jest.fn(() => mockPlugin)
}))

describe('TTS消息管理器腾讯云TTS集成测试', () => {
  beforeEach(() => {
    // 清理状态
    ttsMessageManager.stop()
    
    // 重置mock
    jest.clearAllMocks()
    
    // 设置默认的成功响应
    mockPlugin.textToSpeech.mockImplementation((options: any) => {
      setTimeout(() => {
        options.success({
          result: {
            filePath: 'mock-audio-url.mp3'
          }
        })
      }, 100)
    })
  })

  test('基本TTS转换功能', async () => {
    const messageId = 'tencent-test-1'
    const content = '你好，这是腾讯云TTS测试。'
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 等待TTS处理
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 验证腾讯云TTS被调用
    expect(mockPlugin.textToSpeech).toHaveBeenCalled()
    
    const callArgs = mockPlugin.textToSpeech.mock.calls[0][0]
    expect(callArgs.content).toBe(content)
    expect(callArgs.voiceType).toBe(501004)
    expect(callArgs.language).toBe(1)
  })

  test('多句子TTS转换', async () => {
    const messageId = 'tencent-test-2'
    const content = '第一句话。第二句话！第三句话？'
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 等待TTS处理
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 验证多次调用TTS
    expect(mockPlugin.textToSpeech).toHaveBeenCalledTimes(3)
    
    // 验证每次调用的内容
    const calls = mockPlugin.textToSpeech.mock.calls
    expect(calls[0][0].content).toBe('第一句话')
    expect(calls[1][0].content).toBe('第二句话')
    expect(calls[2][0].content).toBe('第三句话')
  })

  test('TTS转换失败处理', async () => {
    // 设置失败响应
    mockPlugin.textToSpeech.mockImplementation((options: any) => {
      setTimeout(() => {
        options.fail({
          errMsg: 'TTS转换失败'
        })
      }, 100)
    })
    
    const messageId = 'tencent-test-3'
    const content = '这是一个会失败的测试。'
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 等待TTS处理
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 验证TTS被调用
    expect(mockPlugin.textToSpeech).toHaveBeenCalled()
    
    // 验证消息状态
    const messageStatus = ttsMessageManager.getMessageStatus(messageId)
    expect(messageStatus).toBeDefined()
    expect(messageStatus!.sentences.length).toBe(1)
  })

  test('流式消息TTS处理', async () => {
    const messageId = 'tencent-test-4'
    
    // 模拟流式输入
    ttsMessageManager.processMessage(messageId, '这是流式', false)
    await new Promise(resolve => setTimeout(resolve, 150))
    
    ttsMessageManager.processMessage(messageId, '消息的第一部分，', false)
    await new Promise(resolve => setTimeout(resolve, 150))
    
    ttsMessageManager.processMessage(messageId, '第二部分。', true)
    await new Promise(resolve => setTimeout(resolve, 150))
    
    // 验证TTS调用
    expect(mockPlugin.textToSpeech).toHaveBeenCalled()
    
    // 验证最终消息状态
    const messageStatus = ttsMessageManager.getMessageStatus(messageId)
    expect(messageStatus!.content).toBe('这是流式消息的第一部分，第二部分。')
    expect(messageStatus!.isComplete).toBe(true)
  })

  test('新消息中断TTS', async () => {
    const messageId1 = 'tencent-test-5'
    const messageId2 = 'tencent-test-6'
    
    // 开始处理第一条消息
    ttsMessageManager.processMessage(messageId1, '第一条消息，包含多个句子。第二个句子。', false)
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 发送第二条消息（应该中断第一条）
    ttsMessageManager.processMessage(messageId2, '第二条消息。', true)
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 验证当前处理的是第二条消息
    const processingStatus = ttsMessageManager.getProcessingStatus()
    expect(processingStatus.currentMessageId).toBe(messageId2)
    
    // 验证第一条消息的待处理队列被清空
    const message1Status = ttsMessageManager.getMessageStatus(messageId1)
    expect(message1Status!.pendingSentences.length).toBe(0)
  })

  test('音频播放队列管理', async () => {
    const messageId = 'tencent-test-7'
    const content = '第一句。第二句。第三句。'
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 等待TTS处理
    await new Promise(resolve => setTimeout(resolve, 400))
    
    // 验证音频上下文创建
    expect(mockTaro.createInnerAudioContext).toHaveBeenCalled()
    
    // 验证TTS调用次数
    expect(mockPlugin.textToSpeech).toHaveBeenCalledTimes(3)
  })

  test('停止TTS播放', () => {
    const messageId = 'tencent-test-8'
    
    // 开始处理消息
    ttsMessageManager.processMessage(messageId, '这是一条测试消息。', false)
    
    // 停止处理
    ttsMessageManager.stop()
    
    // 验证状态被重置
    const processingStatus = ttsMessageManager.getProcessingStatus()
    expect(processingStatus.currentMessageId).toBeNull()
    expect(processingStatus.isProcessing).toBe(false)
  })

  test('长句子切割和TTS', async () => {
    const messageId = 'tencent-test-9'
    const longSentence = '这是一个非常长的句子，'.repeat(15) + '需要被切割。'
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, longSentence, true)
    
    // 等待TTS处理
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 验证句子被切割
    const messageStatus = ttsMessageManager.getMessageStatus(messageId)
    expect(messageStatus!.sentences.length).toBeGreaterThan(1)
    
    // 验证每个句子都不超过100字符
    messageStatus!.sentences.forEach(sentence => {
      expect(sentence.length).toBeLessThanOrEqual(100)
    })
    
    // 验证TTS被调用多次
    expect(mockPlugin.textToSpeech).toHaveBeenCalledTimes(messageStatus!.sentences.length)
  })
})

// 手动测试函数（用于开发时调试）
export function manualTencentTTSTest() {
  console.log('开始腾讯云TTS手动测试...')
  
  // 测试基本功能
  console.log('\n=== 测试1: 基本TTS功能 ===')
  ttsMessageManager.processMessage('manual-1', '你好，这是腾讯云TTS测试。', true)
  
  setTimeout(() => {
    const status = ttsMessageManager.getMessageStatus('manual-1')
    console.log('消息状态:', status)
    console.log('处理状态:', ttsMessageManager.getProcessingStatus())
  }, 1000)
  
  // 测试多句子
  setTimeout(() => {
    console.log('\n=== 测试2: 多句子TTS ===')
    ttsMessageManager.processMessage('manual-2', '第一句话。第二句话！第三句话？', true)
  }, 2000)
  
  // 测试中断功能
  setTimeout(() => {
    console.log('\n=== 测试3: 中断功能 ===')
    ttsMessageManager.processMessage('manual-3', '这条消息会被中断。', false)
    
    setTimeout(() => {
      ttsMessageManager.processMessage('manual-4', '这是新消息。', true)
    }, 500)
  }, 4000)
}
