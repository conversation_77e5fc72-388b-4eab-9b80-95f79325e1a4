// TTS消息管理器测试文件
// 用于测试文本切割和消息处理功能

import { ttsMessageManager } from './ttsMessageManager'

// 模拟TTS函数，避免实际调用
jest.mock('./qwenTTS', () => ({
  synthesizeQwenTTSRealtime: jest.fn().mockResolvedValue(undefined),
  stopQwenTTSPlayback: jest.fn(),
  enableQwenTTSPlayback: jest.fn()
}))

describe('TTS消息管理器测试', () => {
  beforeEach(() => {
    // 每个测试前清理状态
    ttsMessageManager.stop()
  })

  test('基本文本切割功能', () => {
    const messageId = 'test-message-1'
    const content = '你好，这是第一句话。这是第二句话！这是第三句话？'
    
    ttsMessageManager.processMessage(messageId, content, true)
    
    const status = ttsMessageManager.getMessageStatus(messageId)
    expect(status).toBeDefined()
    expect(status!.sentences.length).toBeGreaterThan(1)
    expect(status!.sentences).toContain('你好')
    expect(status!.sentences).toContain('这是第一句话')
  })

  test('特殊符号去除功能', () => {
    const messageId = 'test-message-2'
    const content = '**这是粗体文本**，*这是斜体*，还有-减号-和<尖括号>需要去除。'
    
    ttsMessageManager.processMessage(messageId, content, true)
    
    const status = ttsMessageManager.getMessageStatus(messageId)
    expect(status).toBeDefined()
    
    // 检查是否去除了特殊符号
    const allText = status!.sentences.join('')
    expect(allText).not.toContain('**')
    expect(allText).not.toContain('<')
    expect(allText).not.toContain('>')
    expect(allText).not.toContain('-')
  })

  test('长句子切割功能', () => {
    const messageId = 'test-message-3'
    // 创建一个超过100字符的长句子
    const longSentence = '这是一个非常长的句子，'.repeat(20) // 大约200字符
    
    ttsMessageManager.processMessage(messageId, longSentence, true)
    
    const status = ttsMessageManager.getMessageStatus(messageId)
    expect(status).toBeDefined()
    
    // 检查是否被切割成多个句子
    expect(status!.sentences.length).toBeGreaterThan(1)
    
    // 检查每个句子都不超过100字符
    status!.sentences.forEach(sentence => {
      expect(sentence.length).toBeLessThanOrEqual(100)
    })
  })

  test('流式消息处理', () => {
    const messageId = 'test-message-4'
    
    // 模拟流式输入
    ttsMessageManager.processMessage(messageId, '这是第一部分，', false)
    ttsMessageManager.processMessage(messageId, '这是第二部分。', false)
    ttsMessageManager.processMessage(messageId, '这是最后部分！', true)
    
    const status = ttsMessageManager.getMessageStatus(messageId)
    expect(status).toBeDefined()
    expect(status!.content).toBe('这是第一部分，这是第二部分。这是最后部分！')
    expect(status!.isComplete).toBe(true)
  })

  test('新消息中断当前处理', () => {
    const messageId1 = 'test-message-5'
    const messageId2 = 'test-message-6'
    
    // 处理第一条消息
    ttsMessageManager.processMessage(messageId1, '第一条消息的内容。', false)
    
    let status1 = ttsMessageManager.getMessageStatus(messageId1)
    expect(status1).toBeDefined()
    
    // 处理第二条消息（应该中断第一条）
    ttsMessageManager.processMessage(messageId2, '第二条消息的内容。', false)
    
    const processingStatus = ttsMessageManager.getProcessingStatus()
    expect(processingStatus.currentMessageId).toBe(messageId2)
    
    // 第一条消息的待处理队列应该被清空
    status1 = ttsMessageManager.getMessageStatus(messageId1)
    expect(status1!.pendingSentences.length).toBe(0)
  })

  test('混合符号切割', () => {
    const messageId = 'test-message-7'
    const content = '第一句；第二句，第三句。第四句！第五句？\n第六句'
    
    ttsMessageManager.processMessage(messageId, content, true)
    
    const status = ttsMessageManager.getMessageStatus(messageId)
    expect(status).toBeDefined()
    expect(status!.sentences.length).toBe(6)
  })

  test('空内容和无效输入处理', () => {
    const messageId = 'test-message-8'
    
    // 测试空内容
    ttsMessageManager.processMessage(messageId, '', true)
    let status = ttsMessageManager.getMessageStatus(messageId)
    expect(status!.sentences.length).toBe(0)
    
    // 测试只有符号的内容
    ttsMessageManager.processMessage(messageId, '，。！？；', true)
    status = ttsMessageManager.getMessageStatus(messageId)
    expect(status!.sentences.length).toBe(0)
  })

  test('消息状态管理', () => {
    const messageId = 'test-message-9'
    
    // 初始状态
    let processingStatus = ttsMessageManager.getProcessingStatus()
    expect(processingStatus.currentMessageId).toBeNull()
    expect(processingStatus.isProcessing).toBe(false)
    
    // 处理消息后
    ttsMessageManager.processMessage(messageId, '测试消息内容。', true)
    
    processingStatus = ttsMessageManager.getProcessingStatus()
    expect(processingStatus.currentMessageId).toBe(messageId)
  })
})

// 手动测试函数（用于开发时调试）
export function manualTest() {
  console.log('开始TTS消息管理器手动测试...')
  
  // 测试1: 基本功能
  console.log('\n=== 测试1: 基本文本切割 ===')
  ttsMessageManager.processMessage('msg1', '你好，世界！这是一个测试。', true)
  console.log('消息状态:', ttsMessageManager.getMessageStatus('msg1'))
  
  // 测试2: 长文本切割
  console.log('\n=== 测试2: 长文本切割 ===')
  const longText = '这是一个很长的测试文本，'.repeat(15) + '结束。'
  ttsMessageManager.processMessage('msg2', longText, true)
  console.log('长文本消息状态:', ttsMessageManager.getMessageStatus('msg2'))
  
  // 测试3: 流式输入
  console.log('\n=== 测试3: 流式输入 ===')
  ttsMessageManager.processMessage('msg3', '流式输入', false)
  ttsMessageManager.processMessage('msg3', '的第一部分，', false)
  ttsMessageManager.processMessage('msg3', '第二部分。', true)
  console.log('流式消息状态:', ttsMessageManager.getMessageStatus('msg3'))
  
  console.log('\n处理状态:', ttsMessageManager.getProcessingStatus())
}
