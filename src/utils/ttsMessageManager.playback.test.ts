// TTS消息管理器播放顺序测试
// 专门测试音频播放的顺序控制和防覆盖功能

import { ttsMessageManager } from './ttsMessageManager'

// 模拟腾讯云TTS插件
const mockPlugin = {
  setQCloudSecret: jest.fn(),
  textToSpeech: jest.fn()
}

// 模拟音频上下文
const createMockAudioContext = () => {
  const mockContext = {
    src: '',
    autoplay: false,
    onPlay: jest.fn(),
    onEnded: jest.fn(),
    onError: jest.fn(),
    play: jest.fn(),
    stop: jest.fn(),
    destroy: jest.fn()
  }
  
  // 模拟播放行为
  mockContext.play.mockImplementation(() => {
    setTimeout(() => {
      if (mockContext.onPlay.mock.calls.length > 0) {
        mockContext.onPlay.mock.calls[0][0]()
      }
    }, 10)
    
    // 模拟播放结束
    setTimeout(() => {
      if (mockContext.onEnded.mock.calls.length > 0) {
        mockContext.onEnded.mock.calls[0][0]()
      }
    }, 100)
  })
  
  return mockContext
}

// 模拟Taro
const mockTaro = {
  createInnerAudioContext: jest.fn(() => createMockAudioContext())
}

// 模拟requirePlugin
jest.mock('@tarojs/taro', () => ({
  ...jest.requireActual('@tarojs/taro'),
  createInnerAudioContext: mockTaro.createInnerAudioContext,
  requirePlugin: jest.fn(() => mockPlugin)
}))

describe('TTS播放顺序控制测试', () => {
  beforeEach(() => {
    // 清理状态
    ttsMessageManager.stop()
    
    // 重置mock
    jest.clearAllMocks()
    
    // 设置TTS成功响应
    mockPlugin.textToSpeech.mockImplementation((options: any) => {
      setTimeout(() => {
        options.success({
          result: {
            filePath: `mock-audio-${Date.now()}-${Math.random()}.mp3`
          }
        })
      }, 50)
    })
  })

  test('单个句子播放', async () => {
    const messageId = 'playback-test-1'
    const content = '这是一个测试句子。'
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 等待TTS处理和播放
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 验证TTS被调用
    expect(mockPlugin.textToSpeech).toHaveBeenCalledTimes(1)
    
    // 验证音频上下文被创建
    expect(mockTaro.createInnerAudioContext).toHaveBeenCalled()
    
    // 验证播放状态
    const audioStatus = ttsMessageManager.getAudioStatus()
    console.log('单句播放状态:', audioStatus)
  })

  test('多个句子顺序播放', async () => {
    const messageId = 'playback-test-2'
    const content = '第一句话。第二句话。第三句话。'
    
    // 记录播放顺序
    const playOrder: string[] = []
    
    // 重新设置TTS mock，记录播放顺序
    mockPlugin.textToSpeech.mockImplementation((options: any) => {
      const content = options.content
      playOrder.push(content)
      
      setTimeout(() => {
        options.success({
          result: {
            filePath: `mock-audio-${content.replace(/[^a-zA-Z0-9]/g, '')}.mp3`
          }
        })
      }, 50)
    })
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 等待所有TTS处理完成
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 验证播放顺序
    expect(playOrder).toEqual(['第一句话', '第二句话', '第三句话'])
    
    // 验证TTS调用次数
    expect(mockPlugin.textToSpeech).toHaveBeenCalledTimes(3)
    
    console.log('多句播放顺序:', playOrder)
  })

  test('快速连续消息不会覆盖', async () => {
    const messageId1 = 'playback-test-3'
    const messageId2 = 'playback-test-4'
    
    const playOrder: string[] = []
    
    // 设置TTS mock记录播放顺序
    mockPlugin.textToSpeech.mockImplementation((options: any) => {
      const content = options.content
      playOrder.push(content)
      
      setTimeout(() => {
        options.success({
          result: {
            filePath: `mock-audio-${Date.now()}.mp3`
          }
        })
      }, 30)
    })
    
    // 快速发送两条消息
    ttsMessageManager.processMessage(messageId1, '第一条消息的内容。', true)
    
    // 立即发送第二条消息（应该中断第一条）
    setTimeout(() => {
      ttsMessageManager.processMessage(messageId2, '第二条消息的内容。', true)
    }, 10)
    
    // 等待处理完成
    await new Promise(resolve => setTimeout(resolve, 400))
    
    // 验证第二条消息被处理
    const processingStatus = ttsMessageManager.getProcessingStatus()
    expect(processingStatus.currentMessageId).toBe(messageId2)
    
    // 验证第一条消息的待处理队列被清空
    const message1Status = ttsMessageManager.getMessageStatus(messageId1)
    expect(message1Status!.pendingSentences.length).toBe(0)
    
    console.log('快速切换播放顺序:', playOrder)
  })

  test('播放状态正确管理', async () => {
    const messageId = 'playback-test-5'
    const content = '第一句。第二句。第三句。'
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 检查初始状态
    let audioStatus = ttsMessageManager.getAudioStatus()
    console.log('初始音频状态:', audioStatus)
    
    // 等待第一个TTS完成
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 检查播放中状态
    audioStatus = ttsMessageManager.getAudioStatus()
    console.log('播放中音频状态:', audioStatus)
    
    // 等待所有播放完成
    await new Promise(resolve => setTimeout(resolve, 400))
    
    // 检查完成后状态
    audioStatus = ttsMessageManager.getAudioStatus()
    console.log('完成后音频状态:', audioStatus)
    
    // 验证最终状态
    expect(audioStatus.queueLength).toBe(0)
  })

  test('停止播放功能', async () => {
    const messageId = 'playback-test-6'
    const content = '这是一个会被停止的消息。包含多个句子。第二个句子。第三个句子。'
    
    // 开始处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 等待开始播放
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 检查播放状态
    let audioStatus = ttsMessageManager.getAudioStatus()
    console.log('停止前音频状态:', audioStatus)
    
    // 停止播放
    ttsMessageManager.stop()
    
    // 检查停止后状态
    audioStatus = ttsMessageManager.getAudioStatus()
    console.log('停止后音频状态:', audioStatus)
    
    // 验证状态被清理
    expect(audioStatus.isPlaying).toBe(false)
    expect(audioStatus.queueLength).toBe(0)
    expect(audioStatus.hasCurrentAudio).toBe(false)
  })

  test('TTS转换失败不影响后续播放', async () => {
    const messageId = 'playback-test-7'
    const content = '正常句子。失败句子。后续句子。'
    
    let callCount = 0
    
    // 设置第二次调用失败
    mockPlugin.textToSpeech.mockImplementation((options: any) => {
      callCount++
      
      setTimeout(() => {
        if (callCount === 2) {
          // 第二次调用失败
          options.fail({
            errMsg: '模拟TTS失败'
          })
        } else {
          // 其他调用成功
          options.success({
            result: {
              filePath: `mock-audio-${callCount}.mp3`
            }
          })
        }
      }, 50)
    })
    
    // 处理消息
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 等待处理完成
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 验证所有句子都被尝试处理
    expect(mockPlugin.textToSpeech).toHaveBeenCalledTimes(3)
    
    // 验证消息状态
    const messageStatus = ttsMessageManager.getMessageStatus(messageId)
    expect(messageStatus!.sentences.length).toBe(3)
    
    console.log('失败处理测试完成，调用次数:', callCount)
  })
})

// 手动测试播放顺序
export function manualPlaybackTest() {
  console.log('开始播放顺序手动测试...')
  
  // 测试1: 基本顺序播放
  console.log('\n=== 测试1: 基本顺序播放 ===')
  ttsMessageManager.processMessage('manual-1', '第一句。第二句。第三句。', true)
  
  setTimeout(() => {
    const audioStatus = ttsMessageManager.getAudioStatus()
    console.log('播放状态:', audioStatus)
  }, 1000)
  
  // 测试2: 中断播放
  setTimeout(() => {
    console.log('\n=== 测试2: 中断播放 ===')
    ttsMessageManager.processMessage('manual-2', '这条消息会被中断。包含多个句子。', false)
    
    setTimeout(() => {
      console.log('发送新消息中断播放')
      ttsMessageManager.processMessage('manual-3', '这是新消息。', true)
    }, 500)
  }, 3000)
  
  // 测试3: 播放状态监控
  setTimeout(() => {
    console.log('\n=== 测试3: 播放状态监控 ===')
    const interval = setInterval(() => {
      const audioStatus = ttsMessageManager.getAudioStatus()
      const processingStatus = ttsMessageManager.getProcessingStatus()
      
      console.log('实时状态:', {
        音频播放: audioStatus.isPlaying,
        队列长度: audioStatus.queueLength,
        当前消息: processingStatus.currentMessageId,
        处理中: processingStatus.isProcessing
      })
      
      // 如果没有在处理且队列为空，停止监控
      if (!processingStatus.isProcessing && audioStatus.queueLength === 0) {
        clearInterval(interval)
        console.log('监控结束')
      }
    }, 500)
  }, 6000)
}
