# TTS消息管理器使用说明

## 概述

TTS消息管理器是一个专门为处理流式消息并进行文本转语音(TTS)的工具。它使用腾讯云TTS服务，能够智能地处理消息的存储、文本切割、TTS转换和播放控制。

## 主要功能

### 1. 按ID存储消息

- 支持流式消息的累积存储
- 相同ID的消息内容会自动合并
- 自动管理消息的完整性状态

### 2. 智能文本切割

- **符号切割**: 按换行符、逗号、句号、感叹号、分号等符号切割
- **特殊符号清理**: 自动去除减号、星号、尖括号等特殊符号
- **长句处理**: 超过100字符的句子会被进一步切割
- **格式清理**: 去除Markdown格式标记，保留纯文本

### 3. TTS转换控制

- 按句子顺序进行TTS转换
- 支持队列管理，确保播放顺序
- 自动等待上一条播放结束后播放下一条

### 4. 新消息中断机制

- 发送新消息时自动停止当前转换
- 清空未播放音频和未转换句子
- 重置播放状态

## 使用方法

### 基本集成

````typescript
import { ttsMessageManager } from '@/utils/ttsMessageManager'

// 在processMessageEntry函数中集成
function processMessageEntry(entry: any) {
  // ... 现有逻辑 ...

  if ((type === 'ai' || type === 'AIMessageChunk') && content) {
    // 清理文本内容
    const cleanContent = content
      .replace(/!\[.*?\]\(.*?\)/g, '') // 去除markdown图片
      .replace(/```[\s\S]*?```/g, '') // 去除代码块
      .replace(/`[^`]*`/g, '') // 去除行内代码
      .trim()

    if (cleanContent) {
      // 判断消息是否完整
      const isComplete = entry.event === 'values'

      // 使用TTS消息管理器处理
      ttsMessageManager.processMessage(id, cleanContent, isComplete)
    }
  }
}
````

### 发送新消息时停止TTS

```typescript
function sendMessage(text: string) {
  // 停止当前的TTS处理
  ttsMessageManager.stop()

  // 发送消息逻辑...
}
```

## API参考

### 主要方法

#### `processMessage(messageId: string, content: string, isComplete: boolean)`

处理消息内容，进行文本切割和TTS转换。

**参数:**

- `messageId`: 消息的唯一标识符
- `content`: 消息内容（可以是部分内容）
- `isComplete`: 消息是否完整

#### `stop()`

停止当前的TTS处理，清空所有队列和状态。

#### `getMessageStatus(messageId: string)`

获取指定消息的处理状态。

**返回值:**

```typescript
{
  id: string
  content: string
  sentences: string[]
  processedSentences: string[]
  pendingSentences: string[]
  isComplete: boolean
}
```

#### `getProcessingStatus()`

获取当前的处理状态。

**返回值:**

```typescript
{
  currentMessageId: string | null
  isProcessing: boolean
  queueLength: number
}
```

#### `cleanupOldMessages(keepCount: number)`

清理旧消息，只保留最近的N条消息。

#### `enableTTS()`

重新启用TTS功能。

### 文本处理规则

#### 切割符号

- 换行符: `\n`
- 中文标点: `，`、`。`、`！`、`；`、`？`
- 英文标点: `,`、`.`、`!`、`;`、`?`

#### 去除符号

- 特殊符号: `-`、`*`、`<`、`>`、`【`、`】`、`[`、`]`、`(`、`)`、`（`、`）`、`#`、`` ` ``、`"`、`"`、`'`、`'`

#### 长句处理

- 超过100字符的句子会按标点符号进一步切割
- 如果仍然超长，会强制按字符数切割

## 使用示例

### 示例1: 基本使用

```typescript
// 处理流式消息
ttsMessageManager.processMessage('msg-1', '你好，', false)
ttsMessageManager.processMessage('msg-1', '这是一个测试。', true)
```

### 示例2: 监控状态

```typescript
// 获取处理状态
const status = ttsMessageManager.getProcessingStatus()
console.log('当前处理的消息ID:', status.currentMessageId)

// 获取消息详情
const messageStatus = ttsMessageManager.getMessageStatus('msg-1')
console.log('句子数量:', messageStatus?.sentences.length)
```

### 示例3: 错误处理

```typescript
try {
  ttsMessageManager.processMessage('msg-1', '测试内容', true)
} catch (error) {
  console.error('TTS处理出错:', error)
  ttsMessageManager.stop()
  ttsMessageManager.enableTTS()
}
```

## 性能优化建议

### 1. 定期清理

```typescript
// 每分钟清理一次，只保留最近10条消息
setInterval(() => {
  ttsMessageManager.cleanupOldMessages(10)
}, 60000)
```

### 2. 组件卸载时清理

```typescript
useEffect(() => {
  return () => {
    ttsMessageManager.stop()
  }
}, [])
```

### 3. 避免频繁调用

- 不要在每次渲染时都调用TTS方法
- 使用useCallback包装处理函数

## 注意事项

1. **消息ID唯一性**: 确保每条消息都有唯一的ID
2. **内容清理**: 在调用前清理掉不需要朗读的格式标记
3. **错误处理**: 包装TTS调用以处理可能的错误
4. **内存管理**: 定期清理旧消息避免内存泄漏
5. **状态管理**: 在适当的时机停止TTS处理

## 调试工具

### 查看处理状态

```typescript
import { debugTTSManager } from '@/utils/ttsMessageManager.example'

// 显示详细的调试信息
debugTTSManager()
```

### 手动测试

```typescript
import { manualTest } from '@/utils/ttsMessageManager.test'

// 运行手动测试
manualTest()
```

## 故障排除

### 常见问题

1. **TTS不播放**
   - 检查TTS服务是否正常连接
   - 确认消息内容不为空
   - 查看控制台是否有错误信息

2. **文本切割不正确**
   - 检查文本是否包含特殊字符
   - 确认切割符号配置是否正确

3. **新消息不中断旧消息**
   - 确认在发送新消息时调用了`ttsMessageManager.stop()`
   - 检查消息ID是否正确

4. **内存占用过高**
   - 定期调用`cleanupOldMessages()`清理旧消息
   - 检查是否有内存泄漏

### 日志调试

```typescript
// 启用详细日志
console.log('TTS状态:', ttsMessageManager.getProcessingStatus())
console.log('消息详情:', ttsMessageManager.getMessageStatus('your-message-id'))
```
