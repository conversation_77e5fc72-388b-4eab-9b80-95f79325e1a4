// TTS消息管理器演示文件
// 用于演示和测试TTS消息管理器的各种功能

import { ttsMessageManager } from './ttsMessageManager'

/**
 * 演示基本功能
 */
export function demoBasicFunctionality() {
  console.log('=== TTS消息管理器基本功能演示 ===\n')
  
  // 清理之前的状态
  ttsMessageManager.stop()
  
  // 演示1: 简单消息处理
  console.log('1. 简单消息处理:')
  ttsMessageManager.processMessage('demo-1', '你好，这是一个简单的测试消息。', true)
  
  const message1 = ttsMessageManager.getMessageStatus('demo-1')
  console.log('   消息内容:', message1?.content)
  console.log('   句子数量:', message1?.sentences.length)
  console.log('   句子列表:', message1?.sentences)
  console.log('')
  
  // 演示2: 流式消息处理
  console.log('2. 流式消息处理:')
  ttsMessageManager.processMessage('demo-2', '这是流式消息', false)
  ttsMessageManager.processMessage('demo-2', '的第一部分，', false)
  ttsMessageManager.processMessage('demo-2', '第二部分。', false)
  ttsMessageManager.processMessage('demo-2', '最后部分！', true)
  
  const message2 = ttsMessageManager.getMessageStatus('demo-2')
  console.log('   完整内容:', message2?.content)
  console.log('   是否完整:', message2?.isComplete)
  console.log('')
  
  // 演示3: 复杂文本切割
  console.log('3. 复杂文本切割:')
  const complexText = '第一句话；第二句话，包含逗号。第三句话！第四句话？\n还有换行符的句子。'
  ttsMessageManager.processMessage('demo-3', complexText, true)
  
  const message3 = ttsMessageManager.getMessageStatus('demo-3')
  console.log('   原始文本:', complexText)
  console.log('   切割结果:', message3?.sentences)
  console.log('')
  
  // 演示4: 长句子处理
  console.log('4. 长句子处理:')
  const longText = '这是一个非常长的句子，'.repeat(15) + '需要被切割成多个部分。'
  ttsMessageManager.processMessage('demo-4', longText, true)
  
  const message4 = ttsMessageManager.getMessageStatus('demo-4')
  console.log('   原始长度:', longText.length)
  console.log('   切割后句子数:', message4?.sentences.length)
  console.log('   每个句子长度:', message4?.sentences.map(s => s.length))
  console.log('')
  
  // 显示统计信息
  const stats = ttsMessageManager.getStatistics()
  console.log('5. 统计信息:')
  console.log('   总消息数:', stats.totalMessages)
  console.log('   总句子数:', stats.totalSentences)
  console.log('   平均每条消息句子数:', stats.averageSentencesPerMessage.toFixed(2))
  console.log('')
}

/**
 * 演示特殊符号处理
 */
export function demoSpecialCharacterHandling() {
  console.log('=== 特殊符号处理演示 ===\n')
  
  ttsMessageManager.stop()
  
  const testCases = [
    {
      name: 'Markdown格式',
      text: '**这是粗体文本**，*这是斜体文本*，`这是代码`。'
    },
    {
      name: '特殊符号',
      text: '包含-减号-和<尖括号>以及【中文括号】的文本。'
    },
    {
      name: '混合格式',
      text: '# 标题\n\n这是一个包含**粗体**、*斜体*、`代码`和[链接](http://example.com)的段落。\n\n- 列表项1\n- 列表项2'
    },
    {
      name: '代码块',
      text: '这是普通文本。\n\n```javascript\nfunction test() {\n  console.log("hello");\n}\n```\n\n这是后续文本。'
    }
  ]
  
  testCases.forEach((testCase, index) => {
    const messageId = `special-${index}`
    console.log(`${index + 1}. ${testCase.name}:`)
    console.log('   原始文本:', testCase.text)
    
    ttsMessageManager.processMessage(messageId, testCase.text, true)
    const message = ttsMessageManager.getMessageStatus(messageId)
    
    console.log('   处理后内容:', message?.content)
    console.log('   句子列表:', message?.sentences)
    console.log('')
  })
}

/**
 * 演示消息中断功能
 */
export function demoMessageInterruption() {
  console.log('=== 消息中断功能演示 ===\n')
  
  ttsMessageManager.stop()
  
  // 开始处理第一条消息
  console.log('1. 开始处理第一条消息:')
  ttsMessageManager.processMessage('interrupt-1', '这是第一条消息，包含多个句子。第二个句子。第三个句子。', false)
  
  let status1 = ttsMessageManager.getMessageStatus('interrupt-1')
  console.log('   第一条消息状态:', {
    句子数: status1?.sentences.length,
    待处理数: status1?.pendingSentences.length
  })
  
  let processingStatus = ttsMessageManager.getProcessingStatus()
  console.log('   当前处理状态:', {
    当前消息ID: processingStatus.currentMessageId,
    是否处理中: processingStatus.isProcessing
  })
  console.log('')
  
  // 发送第二条消息（应该中断第一条）
  console.log('2. 发送第二条消息（中断第一条）:')
  ttsMessageManager.processMessage('interrupt-2', '这是第二条消息，应该中断第一条。', true)
  
  status1 = ttsMessageManager.getMessageStatus('interrupt-1')
  const status2 = ttsMessageManager.getMessageStatus('interrupt-2')
  processingStatus = ttsMessageManager.getProcessingStatus()
  
  console.log('   第一条消息状态（被中断）:', {
    句子数: status1?.sentences.length,
    待处理数: status1?.pendingSentences.length
  })
  console.log('   第二条消息状态:', {
    句子数: status2?.sentences.length,
    待处理数: status2?.pendingSentences.length
  })
  console.log('   当前处理状态:', {
    当前消息ID: processingStatus.currentMessageId,
    是否处理中: processingStatus.isProcessing
  })
  console.log('')
}

/**
 * 演示性能测试
 */
export function demoPerformanceTest() {
  console.log('=== 性能测试演示 ===\n')
  
  ttsMessageManager.stop()
  
  const startTime = Date.now()
  
  // 处理大量消息
  console.log('1. 处理100条消息...')
  for (let i = 0; i < 100; i++) {
    const content = `这是第${i + 1}条消息。包含多个句子，用于测试性能。第二个句子。第三个句子！`
    ttsMessageManager.processMessage(`perf-${i}`, content, true)
  }
  
  const processingTime = Date.now() - startTime
  const stats = ttsMessageManager.getStatistics()
  
  console.log('   处理时间:', processingTime, 'ms')
  console.log('   统计信息:', stats)
  console.log('')
  
  // 测试清理功能
  console.log('2. 测试清理功能:')
  console.log('   清理前消息数:', stats.totalMessages)
  
  ttsMessageManager.cleanupOldMessages(10)
  const statsAfterCleanup = ttsMessageManager.getStatistics()
  
  console.log('   清理后消息数:', statsAfterCleanup.totalMessages)
  console.log('')
}

/**
 * 演示错误处理
 */
export function demoErrorHandling() {
  console.log('=== 错误处理演示 ===\n')
  
  ttsMessageManager.stop()
  
  // 测试空内容
  console.log('1. 测试空内容:')
  ttsMessageManager.processMessage('error-1', '', true)
  const emptyMessage = ttsMessageManager.getMessageStatus('error-1')
  console.log('   空内容处理结果:', {
    内容长度: emptyMessage?.content.length,
    句子数: emptyMessage?.sentences.length
  })
  console.log('')
  
  // 测试只有符号的内容
  console.log('2. 测试只有符号的内容:')
  ttsMessageManager.processMessage('error-2', '，。！？；***---', true)
  const symbolMessage = ttsMessageManager.getMessageStatus('error-2')
  console.log('   符号内容处理结果:', {
    原始内容: '，。！？；***---',
    处理后内容: symbolMessage?.content,
    句子数: symbolMessage?.sentences.length
  })
  console.log('')
  
  // 测试超长单个句子
  console.log('3. 测试超长单个句子:')
  const veryLongSentence = 'A'.repeat(500) // 500个字符的句子
  ttsMessageManager.processMessage('error-3', veryLongSentence, true)
  const longMessage = ttsMessageManager.getMessageStatus('error-3')
  console.log('   超长句子处理结果:', {
    原始长度: veryLongSentence.length,
    句子数: longMessage?.sentences.length,
    最长句子长度: Math.max(...(longMessage?.sentences.map(s => s.length) || [0]))
  })
  console.log('')
}

/**
 * 运行完整演示
 */
export function runFullDemo() {
  console.log('🎤 TTS消息管理器完整功能演示\n')
  console.log('=' .repeat(50))
  
  try {
    demoBasicFunctionality()
    console.log('=' .repeat(50))
    
    demoSpecialCharacterHandling()
    console.log('=' .repeat(50))
    
    demoMessageInterruption()
    console.log('=' .repeat(50))
    
    demoPerformanceTest()
    console.log('=' .repeat(50))
    
    demoErrorHandling()
    console.log('=' .repeat(50))
    
    console.log('✅ 演示完成！')
    
    // 最终清理
    ttsMessageManager.stop()
    
  } catch (error) {
    console.error('❌ 演示过程中出现错误:', error)
  }
}

// 如果直接运行此文件，执行完整演示
if (typeof window === 'undefined' && typeof global !== 'undefined') {
  // Node.js环境
  runFullDemo()
}
