// TTS消息管理器
// 负责管理消息的存储、文本切割、TTS转换和播放控制

import Taro, { requirePlugin } from '@tarojs/taro'

// 获取腾讯云TTS插件
let plugin = requirePlugin('QCloudAIVoice')
// 设置腾讯云账号信息
plugin.setQCloudSecret(1309533950, 'AKIDe9RVN2VlpfIhtrc9UFo9GH4e2GQWTGrI', 'UmwwmPzQfllbJqBJH2iCWecjkaJ5kFXg', true)

// TTS播放状态管理
let currentAudioContext: Taro.InnerAudioContext | null = null
let audioQueue: string[] = [] // 待播放的音频URL队列
let isPlayingQueue = false // 是否正在播放队列

/**
 * 腾讯云TTS转换函数
 */
function synthesizeTencentTTS(text: string): Promise<void> {
  return new Promise((resolve, reject) => {
    plugin.textToSpeech({
      content: text,
      speed: 0,
      volume: 0,
      voiceType: 501004,
      language: 1,
      projectId: 0,
      sampleRate: 16000,

      success: function (data: any) {
        const url = data.result.filePath
        if (url && url.length > 0) {
          console.log('TTS转换成功，添加到播放队列:', text.substring(0, 30) + '...')

          // 将音频URL加入播放队列
          audioQueue.push(url)

          // 只有在没有播放时才启动播放，避免重复启动
          if (!isPlayingQueue && !currentAudioContext) {
            console.log('启动音频播放队列')
            playNextInQueue()
          } else {
            console.log('音频已在播放中，等待队列处理')
          }

          resolve()
        } else {
          reject(new Error('TTS转换失败：未获取到音频URL'))
        }
      },

      fail: function (error: any) {
        console.error('腾讯云TTS转换失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * 播放队列中的下一个音频
 */
function playNextInQueue(): void {
  if (audioQueue.length === 0) {
    isPlayingQueue = false
    console.log('音频播放队列已清空')
    return
  }

  // 如果已经在播放，不要重复开始
  if (isPlayingQueue && currentAudioContext) {
    console.log('音频正在播放中，等待当前音频结束')
    return
  }

  isPlayingQueue = true
  const url = audioQueue.shift()!

  // 确保停止当前播放的音频
  if (currentAudioContext) {
    try {
      currentAudioContext.stop()
      currentAudioContext.destroy()
    } catch (e) {
      console.warn('停止音频时出错:', e)
    }
    currentAudioContext = null
  }

  // 创建新的音频上下文
  currentAudioContext = Taro.createInnerAudioContext()
  currentAudioContext.src = url

  let hasEnded = false // 防止重复触发

  currentAudioContext.onPlay(() => {
    console.log('开始播放TTS音频:', url.substring(0, 50) + '...')
  })

  currentAudioContext.onEnded(() => {
    if (hasEnded) return
    hasEnded = true

    console.log('TTS音频播放结束')

    // 清理当前音频上下文
    if (currentAudioContext) {
      try {
        currentAudioContext.destroy()
      } catch (e) {
        console.warn('销毁音频上下文时出错:', e)
      }
      currentAudioContext = null
    }

    // 标记当前不在播放状态
    isPlayingQueue = false

    // 延迟播放下一个音频，确保当前音频完全结束
    setTimeout(() => {
      playNextInQueue()
    }, 200)
  })

  currentAudioContext.onError((res: any) => {
    if (hasEnded) return
    hasEnded = true

    console.error('TTS音频播放错误:', res.errMsg)

    // 清理当前音频上下文
    if (currentAudioContext) {
      try {
        currentAudioContext.destroy()
      } catch (e) {
        console.warn('销毁音频上下文时出错:', e)
      }
      currentAudioContext = null
    }

    // 标记当前不在播放状态
    isPlayingQueue = false

    // 继续播放下一个音频
    setTimeout(() => {
      playNextInQueue()
    }, 200)
  })

  // 开始播放
  try {
    currentAudioContext.play()
  } catch (error) {
    console.error('播放音频失败:', error)
    hasEnded = true
    isPlayingQueue = false
    setTimeout(() => playNextInQueue(), 200)
  }
}

/**
 * 停止TTS播放并清空队列
 */
function stopTTSPlayback(): void {
  console.log('停止TTS播放，清空队列')

  // 停止当前播放
  if (currentAudioContext) {
    try {
      currentAudioContext.stop()
      currentAudioContext.destroy()
    } catch (e) {
      console.warn('停止音频播放时出错:', e)
    }
    currentAudioContext = null
  }

  // 清空播放队列
  const queueLength = audioQueue.length
  audioQueue = []
  isPlayingQueue = false

  console.log(`TTS播放已停止，清空了${queueLength}个待播放音频`)
}

/**
 * 启用TTS播放
 */
function enableTTSPlayback(): void {
  console.log('TTS播放已启用')
  // 腾讯云TTS不需要特殊的启用操作
}

// 消息存储接口
interface StoredMessage {
  id: string
  content: string
  sentences: string[] // 切割后的句子
  processedSentences: string[] // 已处理的句子
  pendingSentences: string[] // 待处理的句子
  isComplete: boolean // 消息是否完整
}

// TTS管理器类
class TTSMessageManager {
  private messages: Map<string, StoredMessage> = new Map()
  private currentMessageId: string | null = null
  private isProcessing: boolean = false
  private processingQueue: string[] = []

  // 文本切割的符号
  private readonly splitSymbols = ['\n', '，', '。', '！', '；', ',', '.', '!', ';', '?', '？']

  // 需要去除的特殊符号
  private readonly removeSymbols = ['-', '*', '<', '>', '【', '】', '[', ']', '(', ')', '（', '）', '#', '`', '"', '"', "'", '‘', '’']

  /**
   * 处理消息条目
   * @param messageId 消息ID
   * @param content 消息内容
   * @param isComplete 消息是否完整
   */
  public processMessage(messageId: string, content: string, isComplete: boolean = false): void {
    // 如果是新消息，停止当前处理
    if (this.currentMessageId && this.currentMessageId !== messageId) {
      this.stopCurrentProcessing()
    }

    // 设置当前消息ID
    this.currentMessageId = messageId

    // 获取或创建消息存储
    let storedMessage = this.messages.get(messageId)
    if (!storedMessage) {
      storedMessage = {
        id: messageId,
        content: '',
        sentences: [],
        processedSentences: [],
        pendingSentences: [],
        isComplete: false
      }
      this.messages.set(messageId, storedMessage)
    }

    // 更新消息内容
    storedMessage.content += content
    storedMessage.isComplete = isComplete

    // 重新切割句子
    this.splitMessageIntoSentences(storedMessage)

    // 开始处理新的句子
    this.processNewSentences(messageId)
  }

  /**
   * 将消息内容切割为句子
   */
  private splitMessageIntoSentences(message: StoredMessage): void {
    let text = message.content

    // 去除特殊符号
    for (const symbol of this.removeSymbols) {
      text = text.replace(new RegExp(`\\${symbol}`, 'g'), '')
    }

    // 按符号切割
    let sentences: string[] = [text]
    for (const symbol of this.splitSymbols) {
      const newSentences: string[] = []
      for (const sentence of sentences) {
        const parts = sentence.split(symbol)
        newSentences.push(...parts)
      }
      sentences = newSentences
    }

    // 过滤空句子并处理长句子
    const processedSentences: string[] = []
    for (let sentence of sentences) {
      sentence = sentence.trim()
      if (sentence.length === 0) continue

      // 如果句子超过100字符，按字符数切割
      if (sentence.length > 100) {
        const chunks = this.splitLongSentence(sentence, 100)
        processedSentences.push(...chunks)
      } else {
        processedSentences.push(sentence)
      }
    }

    // 更新句子列表，只添加新的句子
    const previousCount = message.sentences.length
    message.sentences = processedSentences

    // 计算新增的句子
    const newSentences = processedSentences.slice(previousCount)
    message.pendingSentences.push(...newSentences)
  }

  /**
   * 切割长句子
   */
  private splitLongSentence(sentence: string, maxLength: number): string[] {
    const chunks: string[] = []
    let currentChunk = ''

    // 尝试按标点符号切割
    const words = sentence.split(/([，。！；,.\!;?？])/g)

    for (const word of words) {
      if (currentChunk.length + word.length <= maxLength) {
        currentChunk += word
      } else {
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim())
        }
        currentChunk = word
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim())
    }

    // 如果还有超长的块，强制按字符数切割
    const finalChunks: string[] = []
    for (const chunk of chunks) {
      if (chunk.length <= maxLength) {
        finalChunks.push(chunk)
      } else {
        // 强制按字符数切割
        for (let i = 0; i < chunk.length; i += maxLength) {
          finalChunks.push(chunk.slice(i, i + maxLength))
        }
      }
    }

    return finalChunks.filter((chunk) => chunk.trim().length > 0)
  }

  /**
   * 处理新的句子
   */
  private async processNewSentences(messageId: string): Promise<void> {
    const message = this.messages.get(messageId)
    if (!message || message.pendingSentences.length === 0) return

    // 如果当前正在处理其他消息，等待
    if (this.isProcessing && this.currentMessageId !== messageId) {
      return
    }

    this.isProcessing = true

    try {
      while (message.pendingSentences.length > 0) {
        // 检查是否被中断
        if (this.currentMessageId !== messageId) {
          console.log('TTS处理被中断，切换到新消息:', this.currentMessageId)
          break
        }

        const sentence = message.pendingSentences.shift()!

        // 跳过空句子
        if (!sentence.trim()) {
          continue
        }

        try {
          // 调用TTS转换
          await this.convertSentenceToTTS(sentence)

          // 记录已处理的句子
          message.processedSentences.push(sentence)
        } catch (error) {
          console.error('单个句子TTS转换失败:', error, '句子:', sentence)
          // 继续处理下一个句子，不中断整个流程
        }

        // 添加小延迟，避免过快发送
        await new Promise((resolve) => setTimeout(resolve, 100))
      }
    } catch (error) {
      console.error('处理TTS句子时出错:', error)
    } finally {
      // 只有当前消息处理完成时才重置状态
      if (this.currentMessageId === messageId) {
        this.isProcessing = false
      }
    }
  }

  /**
   * 转换句子为TTS
   */
  private async convertSentenceToTTS(sentence: string): Promise<void> {
    try {
      // 验证句子长度
      if (sentence.length > 100) {
        console.warn('句子长度超过100字符，可能影响TTS效果:', sentence.length)
      }

      console.log('转换TTS:', sentence.substring(0, 50) + (sentence.length > 50 ? '...' : ''))
      await synthesizeTencentTTS(sentence)
    } catch (error) {
      console.error('TTS转换失败:', error, '句子:', sentence.substring(0, 50))
      throw error
    }
  }

  /**
   * 停止当前处理
   */
  private stopCurrentProcessing(): void {
    console.log('停止当前TTS处理')

    // 停止TTS播放
    stopTTSPlayback()

    // 重置状态
    this.isProcessing = false
    this.processingQueue = []

    // 清空当前消息的待处理句子
    if (this.currentMessageId) {
      const currentMessage = this.messages.get(this.currentMessageId)
      if (currentMessage) {
        currentMessage.pendingSentences = []
      }
    }
  }

  /**
   * 获取消息状态
   */
  public getMessageStatus(messageId: string): StoredMessage | undefined {
    return this.messages.get(messageId)
  }

  /**
   * 获取当前处理状态
   */
  public getProcessingStatus(): {
    currentMessageId: string | null
    isProcessing: boolean
    queueLength: number
  } {
    return {
      currentMessageId: this.currentMessageId,
      isProcessing: this.isProcessing,
      queueLength: this.processingQueue.length
    }
  }

  /**
   * 清理旧消息（保留最近的N条）
   */
  public cleanupOldMessages(keepCount: number = 10): void {
    if (this.messages.size <= keepCount) return

    const messageIds = Array.from(this.messages.keys())
    const toDelete = messageIds.slice(0, messageIds.length - keepCount)

    for (const id of toDelete) {
      this.messages.delete(id)
    }
  }

  /**
   * 重新启用TTS
   */
  public enableTTS(): void {
    enableTTSPlayback()
  }

  /**
   * 完全停止并清理
   */
  public stop(): void {
    this.stopCurrentProcessing()
    this.messages.clear()
    this.currentMessageId = null
  }

  /**
   * 获取统计信息
   */
  public getStatistics(): {
    totalMessages: number
    totalSentences: number
    totalProcessedSentences: number
    totalPendingSentences: number
    averageSentencesPerMessage: number
  } {
    let totalSentences = 0
    let totalProcessedSentences = 0
    let totalPendingSentences = 0

    for (const message of this.messages.values()) {
      totalSentences += message.sentences.length
      totalProcessedSentences += message.processedSentences.length
      totalPendingSentences += message.pendingSentences.length
    }

    return {
      totalMessages: this.messages.size,
      totalSentences,
      totalProcessedSentences,
      totalPendingSentences,
      averageSentencesPerMessage: this.messages.size > 0 ? totalSentences / this.messages.size : 0
    }
  }
}

// 创建全局实例
export const ttsMessageManager = new TTSMessageManager()

// 导出类型
export type { StoredMessage }
