// TTS播放顺序控制示例
// 展示修复后的播放顺序控制功能

import { ttsMessageManager } from './ttsMessageManager'

/**
 * 示例1: 基本顺序播放
 * 演示多个句子按顺序播放，不会覆盖
 */
export function exampleSequentialPlayback() {
  console.log('=== 示例1: 基本顺序播放 ===')
  
  const messageId = 'sequential-example'
  const content = '这是第一句话。这是第二句话！这是第三句话？最后一句话。'
  
  // 处理消息
  ttsMessageManager.processMessage(messageId, content, true)
  
  // 监控播放状态
  const monitorInterval = setInterval(() => {
    const audioStatus = ttsMessageManager.getAudioStatus()
    const processingStatus = ttsMessageManager.getProcessingStatus()
    
    console.log('播放状态:', {
      正在播放: audioStatus.isPlaying,
      队列长度: audioStatus.queueLength,
      有音频: audioStatus.hasCurrentAudio,
      处理中: processingStatus.isProcessing
    })
    
    // 如果播放完成，停止监控
    if (!audioStatus.isPlaying && audioStatus.queueLength === 0 && !processingStatus.isProcessing) {
      clearInterval(monitorInterval)
      console.log('播放完成！')
    }
  }, 500)
}

/**
 * 示例2: 新消息中断播放
 * 演示新消息如何正确中断当前播放
 */
export function exampleInterruptPlayback() {
  console.log('\n=== 示例2: 新消息中断播放 ===')
  
  // 开始播放第一条消息
  const messageId1 = 'interrupt-example-1'
  const content1 = '这是第一条消息，包含很多句子。第二句。第三句。第四句。第五句。'
  
  console.log('开始播放第一条消息...')
  ttsMessageManager.processMessage(messageId1, content1, true)
  
  // 2秒后发送新消息中断
  setTimeout(() => {
    console.log('发送新消息，中断当前播放...')
    
    const messageId2 = 'interrupt-example-2'
    const content2 = '这是新消息，应该中断前面的播放。'
    
    ttsMessageManager.processMessage(messageId2, content2, true)
    
    // 检查中断效果
    setTimeout(() => {
      const processingStatus = ttsMessageManager.getProcessingStatus()
      const message1Status = ttsMessageManager.getMessageStatus(messageId1)
      
      console.log('中断后状态:', {
        当前消息ID: processingStatus.currentMessageId,
        第一条消息待处理: message1Status?.pendingSentences.length
      })
    }, 100)
  }, 2000)
}

/**
 * 示例3: 流式消息播放
 * 演示流式消息的播放控制
 */
export function exampleStreamingPlayback() {
  console.log('\n=== 示例3: 流式消息播放 ===')
  
  const messageId = 'streaming-example'
  
  // 模拟流式输入
  console.log('开始流式输入...')
  
  ttsMessageManager.processMessage(messageId, '这是流式消息', false)
  
  setTimeout(() => {
    ttsMessageManager.processMessage(messageId, '的第一部分，', false)
  }, 500)
  
  setTimeout(() => {
    ttsMessageManager.processMessage(messageId, '第二部分。', false)
  }, 1000)
  
  setTimeout(() => {
    ttsMessageManager.processMessage(messageId, '最后部分！', true)
    console.log('流式输入完成')
  }, 1500)
  
  // 监控流式播放
  setTimeout(() => {
    const monitorInterval = setInterval(() => {
      const messageStatus = ttsMessageManager.getMessageStatus(messageId)
      const audioStatus = ttsMessageManager.getAudioStatus()
      
      if (messageStatus) {
        console.log('流式播放状态:', {
          内容长度: messageStatus.content.length,
          句子数: messageStatus.sentences.length,
          已处理: messageStatus.processedSentences.length,
          待处理: messageStatus.pendingSentences.length,
          播放中: audioStatus.isPlaying,
          队列: audioStatus.queueLength
        })
      }
      
      // 如果完成，停止监控
      if (messageStatus?.isComplete && 
          messageStatus.pendingSentences.length === 0 && 
          !audioStatus.isPlaying && 
          audioStatus.queueLength === 0) {
        clearInterval(monitorInterval)
        console.log('流式播放完成！')
      }
    }, 500)
  }, 2000)
}

/**
 * 示例4: 播放状态监控
 * 演示如何监控播放状态
 */
export function examplePlaybackMonitoring() {
  console.log('\n=== 示例4: 播放状态监控 ===')
  
  const messageId = 'monitoring-example'
  const content = '监控播放状态。第二句。第三句。第四句。第五句。'
  
  // 开始播放
  ttsMessageManager.processMessage(messageId, content, true)
  
  // 详细监控
  let monitorCount = 0
  const detailedMonitor = setInterval(() => {
    monitorCount++
    
    const audioStatus = ttsMessageManager.getAudioStatus()
    const processingStatus = ttsMessageManager.getProcessingStatus()
    const messageStatus = ttsMessageManager.getMessageStatus(messageId)
    const statistics = ttsMessageManager.getStatistics()
    
    console.log(`监控 ${monitorCount}:`, {
      时间: new Date().toLocaleTimeString(),
      音频状态: {
        播放中: audioStatus.isPlaying,
        队列长度: audioStatus.queueLength,
        有当前音频: audioStatus.hasCurrentAudio
      },
      处理状态: {
        当前消息: processingStatus.currentMessageId,
        处理中: processingStatus.isProcessing
      },
      消息状态: messageStatus ? {
        句子总数: messageStatus.sentences.length,
        已处理: messageStatus.processedSentences.length,
        待处理: messageStatus.pendingSentences.length
      } : null,
      统计信息: {
        总消息数: statistics.totalMessages,
        总句子数: statistics.totalSentences
      }
    })
    
    // 监控20次或播放完成后停止
    if (monitorCount >= 20 || 
        (!audioStatus.isPlaying && audioStatus.queueLength === 0 && !processingStatus.isProcessing)) {
      clearInterval(detailedMonitor)
      console.log('监控结束')
    }
  }, 300)
}

/**
 * 示例5: 错误处理和恢复
 * 演示播放过程中的错误处理
 */
export function exampleErrorHandling() {
  console.log('\n=== 示例5: 错误处理和恢复 ===')
  
  try {
    const messageId = 'error-example'
    const content = '这是错误处理测试。第二句。第三句。'
    
    // 开始播放
    ttsMessageManager.processMessage(messageId, content, true)
    
    // 模拟错误情况
    setTimeout(() => {
      console.log('模拟错误，停止播放...')
      ttsMessageManager.stop()
      
      // 恢复播放
      setTimeout(() => {
        console.log('恢复播放...')
        ttsMessageManager.enableTTS()
        ttsMessageManager.processMessage('recovery-example', '恢复播放测试。', true)
      }, 1000)
    }, 2000)
    
  } catch (error) {
    console.error('播放过程中出现错误:', error)
    
    // 错误恢复
    ttsMessageManager.stop()
    ttsMessageManager.enableTTS()
  }
}

/**
 * 运行所有示例
 */
export function runAllPlaybackExamples() {
  console.log('🎵 TTS播放顺序控制示例演示')
  console.log('=' .repeat(50))
  
  // 示例1: 基本顺序播放
  exampleSequentialPlayback()
  
  // 示例2: 中断播放 (5秒后)
  setTimeout(() => {
    exampleInterruptPlayback()
  }, 5000)
  
  // 示例3: 流式播放 (12秒后)
  setTimeout(() => {
    exampleStreamingPlayback()
  }, 12000)
  
  // 示例4: 状态监控 (20秒后)
  setTimeout(() => {
    examplePlaybackMonitoring()
  }, 20000)
  
  // 示例5: 错误处理 (30秒后)
  setTimeout(() => {
    exampleErrorHandling()
  }, 30000)
  
  console.log('所有示例已启动，请观察控制台输出...')
}

/**
 * 快速测试播放顺序
 */
export function quickPlaybackTest() {
  console.log('🚀 快速播放顺序测试')
  
  // 清理状态
  ttsMessageManager.stop()
  
  // 测试多句子播放
  const messageId = 'quick-test'
  const content = '第一句。第二句。第三句。'
  
  console.log('开始播放:', content)
  ttsMessageManager.processMessage(messageId, content, true)
  
  // 监控5秒
  let count = 0
  const monitor = setInterval(() => {
    count++
    const audioStatus = ttsMessageManager.getAudioStatus()
    
    console.log(`${count}秒: 播放=${audioStatus.isPlaying}, 队列=${audioStatus.queueLength}`)
    
    if (count >= 5 || (!audioStatus.isPlaying && audioStatus.queueLength === 0)) {
      clearInterval(monitor)
      console.log('测试完成')
    }
  }, 1000)
}
