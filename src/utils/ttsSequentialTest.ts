// TTS顺序播放测试
// 专门测试修复后的顺序播放功能

import { ttsMessageManager } from './ttsMessageManager'

/**
 * 简单的顺序播放测试
 */
export function testSequentialPlayback() {
  console.log('🎵 开始TTS顺序播放测试')
  console.log('=' .repeat(50))
  
  // 清理之前的状态
  ttsMessageManager.stop()
  
  // 测试消息
  const messageId = 'sequential-test'
  const content = '第一句话。第二句话。第三句话。第四句话。'
  
  console.log('📝 测试内容:', content)
  console.log('🚀 开始处理...')
  
  // 开始处理
  ttsMessageManager.processMessage(messageId, content, true)
  
  // 监控播放状态
  let monitorCount = 0
  const monitor = setInterval(() => {
    monitorCount++
    
    const audioStatus = ttsMessageManager.getAudioStatus()
    const processingStatus = ttsMessageManager.getProcessingStatus()
    const messageStatus = ttsMessageManager.getMessageStatus(messageId)
    
    console.log(`⏰ 监控 ${monitorCount}:`, {
      时间: new Date().toLocaleTimeString(),
      播放中: audioStatus.isPlaying,
      队列长度: audioStatus.queueLength,
      有音频: audioStatus.hasCurrentAudio,
      处理中: processingStatus.isProcessing,
      已处理句子: messageStatus?.processedSentences.length || 0,
      待处理句子: messageStatus?.pendingSentences.length || 0
    })
    
    // 如果播放完成或监控超时，停止监控
    if (monitorCount >= 30 || 
        (!audioStatus.isPlaying && 
         audioStatus.queueLength === 0 && 
         !processingStatus.isProcessing &&
         (messageStatus?.pendingSentences.length || 0) === 0)) {
      clearInterval(monitor)
      
      console.log('✅ 测试完成!')
      console.log('📊 最终状态:', {
        总句子数: messageStatus?.sentences.length || 0,
        已处理: messageStatus?.processedSentences.length || 0,
        待处理: messageStatus?.pendingSentences.length || 0
      })
    }
  }, 1000)
}

/**
 * 中断播放测试
 */
export function testInterruptPlayback() {
  console.log('\n🛑 开始中断播放测试')
  console.log('=' .repeat(50))
  
  // 清理状态
  ttsMessageManager.stop()
  
  // 第一条消息
  const messageId1 = 'interrupt-test-1'
  const content1 = '这是第一条消息。包含多个句子。第三句。第四句。第五句。'
  
  console.log('📝 第一条消息:', content1)
  console.log('🚀 开始播放第一条消息...')
  
  ttsMessageManager.processMessage(messageId1, content1, true)
  
  // 2秒后发送第二条消息
  setTimeout(() => {
    const messageId2 = 'interrupt-test-2'
    const content2 = '这是中断消息。应该停止前面的播放。'
    
    console.log('\n🔄 发送中断消息:', content2)
    ttsMessageManager.processMessage(messageId2, content2, true)
    
    // 监控中断效果
    setTimeout(() => {
      const processingStatus = ttsMessageManager.getProcessingStatus()
      const message1Status = ttsMessageManager.getMessageStatus(messageId1)
      const audioStatus = ttsMessageManager.getAudioStatus()
      
      console.log('🔍 中断后状态检查:', {
        当前处理消息: processingStatus.currentMessageId,
        第一条消息待处理: message1Status?.pendingSentences.length || 0,
        音频播放状态: audioStatus.isPlaying,
        队列长度: audioStatus.queueLength
      })
    }, 500)
  }, 2000)
}

/**
 * 详细的播放日志测试
 */
export function testDetailedPlayback() {
  console.log('\n📋 开始详细播放日志测试')
  console.log('=' .repeat(50))
  
  // 清理状态
  ttsMessageManager.stop()
  
  // 测试消息
  const messageId = 'detailed-test'
  const content = '第一句。第二句。第三句。'
  
  console.log('📝 测试内容:', content)
  
  // 开始处理
  ttsMessageManager.processMessage(messageId, content, true)
  
  // 详细监控
  let detailCount = 0
  const detailMonitor = setInterval(() => {
    detailCount++
    
    const audioStatus = ttsMessageManager.getAudioStatus()
    const processingStatus = ttsMessageManager.getProcessingStatus()
    const messageStatus = ttsMessageManager.getMessageStatus(messageId)
    const statistics = ttsMessageManager.getStatistics()
    
    console.log(`\n📊 详细状态 ${detailCount}:`)
    console.log('  🎵 音频状态:', {
      播放中: audioStatus.isPlaying,
      队列长度: audioStatus.queueLength,
      有当前音频: audioStatus.hasCurrentAudio
    })
    console.log('  🔄 处理状态:', {
      当前消息ID: processingStatus.currentMessageId,
      正在处理: processingStatus.isProcessing
    })
    console.log('  📝 消息状态:', messageStatus ? {
      句子总数: messageStatus.sentences.length,
      已处理: messageStatus.processedSentences.length,
      待处理: messageStatus.pendingSentences.length,
      是否完整: messageStatus.isComplete
    } : '无')
    console.log('  📈 统计信息:', {
      总消息数: statistics.totalMessages,
      总句子数: statistics.totalSentences
    })
    
    // 停止条件
    if (detailCount >= 20 || 
        (!audioStatus.isPlaying && 
         audioStatus.queueLength === 0 && 
         !processingStatus.isProcessing)) {
      clearInterval(detailMonitor)
      console.log('\n✅ 详细测试完成!')
    }
  }, 2000)
}

/**
 * 运行所有测试
 */
export function runAllSequentialTests() {
  console.log('🧪 TTS顺序播放修复验证测试')
  console.log('=' .repeat(60))
  
  // 测试1: 基本顺序播放
  testSequentialPlayback()
  
  // 测试2: 中断播放 (15秒后)
  setTimeout(() => {
    testInterruptPlayback()
  }, 15000)
  
  // 测试3: 详细日志 (25秒后)
  setTimeout(() => {
    testDetailedPlayback()
  }, 25000)
  
  console.log('\n📝 说明:')
  console.log('- 观察控制台日志，确认音频按顺序播放')
  console.log('- 检查是否有音频覆盖的情况')
  console.log('- 验证中断功能是否正常工作')
  console.log('- 所有测试将在约45秒内完成')
}

/**
 * 快速验证修复效果
 */
export function quickFixVerification() {
  console.log('⚡ 快速验证修复效果')
  console.log('=' .repeat(40))
  
  // 清理状态
  ttsMessageManager.stop()
  
  // 简单测试
  const messageId = 'quick-fix-test'
  const content = '测试一。测试二。测试三。'
  
  console.log('🎯 测试内容:', content)
  console.log('🔍 关键检查点:')
  console.log('  1. 每个句子完整播放')
  console.log('  2. 没有音频覆盖')
  console.log('  3. 播放顺序正确')
  
  // 开始测试
  ttsMessageManager.processMessage(messageId, content, true)
  
  // 简单监控
  let checkCount = 0
  const quickCheck = setInterval(() => {
    checkCount++
    
    const audioStatus = ttsMessageManager.getAudioStatus()
    const messageStatus = ttsMessageManager.getMessageStatus(messageId)
    
    const status = audioStatus.isPlaying ? '🎵播放中' : '⏸️暂停'
    const queue = audioStatus.queueLength > 0 ? `📋队列:${audioStatus.queueLength}` : '📋队列:空'
    const processed = messageStatus ? `✅已处理:${messageStatus.processedSentences.length}` : '✅已处理:0'
    
    console.log(`⏰ ${checkCount}秒: ${status} | ${queue} | ${processed}`)
    
    if (checkCount >= 10 || 
        (!audioStatus.isPlaying && audioStatus.queueLength === 0)) {
      clearInterval(quickCheck)
      
      const finalStatus = messageStatus
      console.log('\n🏁 快速验证结果:')
      console.log('  📊 句子总数:', finalStatus?.sentences.length || 0)
      console.log('  ✅ 已处理:', finalStatus?.processedSentences.length || 0)
      console.log('  ⏳ 待处理:', finalStatus?.pendingSentences.length || 0)
      
      if ((finalStatus?.processedSentences.length || 0) === (finalStatus?.sentences.length || 0)) {
        console.log('  🎉 验证成功: 所有句子都已处理!')
      } else {
        console.log('  ⚠️  验证警告: 可能存在未处理的句子')
      }
    }
  }, 1000)
}
