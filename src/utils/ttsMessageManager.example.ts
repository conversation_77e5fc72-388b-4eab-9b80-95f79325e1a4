// TTS消息管理器使用示例
// 展示如何在实际项目中集成和使用TTS消息管理器

import { ttsMessageManager } from './ttsMessageManager'

/**
 * 示例1: 在processMessageEntry函数中集成TTS管理器
 * 这是最常见的使用场景，处理流式消息
 */
export function exampleProcessMessageEntry(entry: any) {
  if (!entry.data) return

  let messageData: any
  
  // 根据数据格式提取消息内容
  if (Array.isArray(entry.data)) {
    messageData = entry.data[0]
  } else if (entry.data.messages && Array.isArray(entry.data.messages)) {
    messageData = entry.data.messages[entry.data.messages.length - 1]
  } else {
    return
  }

  if (!messageData || !messageData.id || messageData.content === undefined) return

  const { id, content, type } = messageData

  // 只处理AI消息
  if (type === 'ai' || type === 'AIMessageChunk') {
    // 清理文本内容
    const cleanContent = cleanTextForTTS(content)
    
    if (cleanContent) {
      // 判断消息是否完整
      const isComplete = entry.event === 'values'
      
      // 使用TTS消息管理器处理
      ttsMessageManager.processMessage(id, cleanContent, isComplete)
    }
  }
}

/**
 * 示例2: 清理文本内容的函数
 * 去除各种格式标记，保留纯文本用于TTS
 */
export function cleanTextForTTS(content: string): string {
  return content
    .replace(/!\[.*?\]\(.*?\)/g, '') // 去除markdown图片
    .replace(/```[\s\S]*?```/g, '') // 去除代码块
    .replace(/`[^`]*`/g, '') // 去除行内代码
    .replace(/\*\*([^*]*)\*\*/g, '$1') // 去除粗体标记，保留内容
    .replace(/\*([^*]*)\*/g, '$1') // 去除斜体标记，保留内容
    .replace(/\[([^\]]*)\]\([^)]*\)/g, '$1') // 去除链接，保留文本
    .replace(/#{1,6}\s*/g, '') // 去除标题标记
    .replace(/>\s*/g, '') // 去除引用标记
    .replace(/[-*+]\s*/g, '') // 去除列表标记
    .replace(/\d+\.\s*/g, '') // 去除有序列表标记
    .trim()
}

/**
 * 示例3: 在发送新消息时停止当前TTS
 * 确保新消息不会与旧消息的TTS冲突
 */
export function exampleSendMessage(text: string) {
  // 停止当前的TTS处理
  ttsMessageManager.stop()
  
  // 发送新消息的逻辑...
  console.log('发送消息:', text)
  
  // 这里会调用实际的API发送消息
  // 然后在processMessageEntry中处理返回的流式数据
}

/**
 * 示例4: 监控TTS处理状态
 * 可以用于显示TTS状态或调试
 */
export function monitorTTSStatus() {
  const status = ttsMessageManager.getProcessingStatus()
  
  console.log('TTS处理状态:', {
    当前消息ID: status.currentMessageId,
    是否正在处理: status.isProcessing,
    队列长度: status.queueLength
  })
  
  // 如果有当前消息，显示详细信息
  if (status.currentMessageId) {
    const messageStatus = ttsMessageManager.getMessageStatus(status.currentMessageId)
    if (messageStatus) {
      console.log('当前消息详情:', {
        消息ID: messageStatus.id,
        内容长度: messageStatus.content.length,
        句子总数: messageStatus.sentences.length,
        已处理句子数: messageStatus.processedSentences.length,
        待处理句子数: messageStatus.pendingSentences.length,
        是否完整: messageStatus.isComplete
      })
    }
  }
}

/**
 * 示例5: 手动控制TTS播放
 * 在某些场景下可能需要手动控制
 */
export function exampleManualControl() {
  // 停止当前播放
  ttsMessageManager.stop()
  
  // 处理单条消息
  const messageId = 'manual-message-1'
  const content = '这是一条手动处理的消息。包含多个句子，会被自动切割。'
  
  ttsMessageManager.processMessage(messageId, content, true)
  
  // 监控处理状态
  setTimeout(() => {
    monitorTTSStatus()
  }, 1000)
}

/**
 * 示例6: 批量处理历史消息
 * 如果需要对历史消息进行TTS处理
 */
export function exampleBatchProcess(messages: Array<{id: string, content: string}>) {
  // 停止当前处理
  ttsMessageManager.stop()
  
  // 逐个处理消息
  messages.forEach((message, index) => {
    const cleanContent = cleanTextForTTS(message.content)
    if (cleanContent) {
      // 最后一条消息标记为完整
      const isComplete = index === messages.length - 1
      ttsMessageManager.processMessage(message.id, cleanContent, isComplete)
    }
  })
}

/**
 * 示例7: 错误处理和恢复
 * 处理TTS过程中可能出现的错误
 */
export function exampleErrorHandling() {
  try {
    // 正常处理消息
    ttsMessageManager.processMessage('test-msg', '测试消息内容。', true)
  } catch (error) {
    console.error('TTS处理出错:', error)
    
    // 错误恢复：停止当前处理并重新启用TTS
    ttsMessageManager.stop()
    ttsMessageManager.enableTTS()
  }
}

/**
 * 示例8: 性能优化 - 清理旧消息
 * 定期清理旧消息以避免内存泄漏
 */
export function exampleCleanup() {
  // 每隔一段时间清理旧消息，只保留最近的10条
  setInterval(() => {
    ttsMessageManager.cleanupOldMessages(10)
    console.log('已清理旧消息')
  }, 60000) // 每分钟清理一次
}

/**
 * 示例9: 调试和测试工具
 * 用于开发时调试TTS功能
 */
export function debugTTSManager() {
  console.log('=== TTS消息管理器调试信息 ===')
  
  // 处理状态
  const processingStatus = ttsMessageManager.getProcessingStatus()
  console.log('处理状态:', processingStatus)
  
  // 如果有当前消息，显示详细信息
  if (processingStatus.currentMessageId) {
    const messageStatus = ttsMessageManager.getMessageStatus(processingStatus.currentMessageId)
    console.log('当前消息:', messageStatus)
  }
  
  // 测试文本切割
  console.log('\n=== 文本切割测试 ===')
  const testTexts = [
    '简单句子。',
    '多个句子，包含逗号。还有感叹号！以及问号？',
    '很长的句子，'.repeat(20) + '需要切割。',
    '包含**粗体**和*斜体*以及`代码`的文本。',
    '包含\n换行符\n的文本。'
  ]
  
  testTexts.forEach((text, index) => {
    const testId = `test-${index}`
    ttsMessageManager.processMessage(testId, text, true)
    const status = ttsMessageManager.getMessageStatus(testId)
    console.log(`测试${index + 1}:`, {
      原文: text,
      句子数: status?.sentences.length,
      句子: status?.sentences
    })
  })
}

/**
 * 示例10: 集成到React组件中
 * 展示如何在React组件中使用TTS管理器
 */
export function exampleReactIntegration() {
  // 这是一个伪代码示例，展示在React组件中的使用方式
  
  /*
  import { useEffect, useCallback } from 'react'
  import { ttsMessageManager } from '@/utils/ttsMessageManager'
  
  function ChatComponent() {
    // 处理消息的回调
    const handleMessage = useCallback((messageId: string, content: string, isComplete: boolean) => {
      const cleanContent = cleanTextForTTS(content)
      if (cleanContent) {
        ttsMessageManager.processMessage(messageId, cleanContent, isComplete)
      }
    }, [])
    
    // 发送消息时停止TTS
    const handleSendMessage = useCallback((text: string) => {
      ttsMessageManager.stop()
      // 发送消息逻辑...
    }, [])
    
    // 组件卸载时清理
    useEffect(() => {
      return () => {
        ttsMessageManager.stop()
      }
    }, [])
    
    // 定期清理旧消息
    useEffect(() => {
      const interval = setInterval(() => {
        ttsMessageManager.cleanupOldMessages(10)
      }, 60000)
      
      return () => clearInterval(interval)
    }, [])
    
    return (
      // JSX内容...
    )
  }
  */
  
  console.log('React集成示例代码请查看注释部分')
}
